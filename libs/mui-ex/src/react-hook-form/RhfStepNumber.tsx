import { Add, Remove } from '@mui/icons-material';
import {
  Box,
  FormHelperText,
  FormLabel,
  IconButton,
  Stack,
  TextField,
  type TextFieldProps,
} from '@mui/material';
import React from 'react';
import { type FieldValues, type UseControllerProps, useController } from 'react-hook-form';
import { z } from 'zod';

type Props<T extends FieldValues> = UseControllerProps<T> &
  Omit<TextFieldProps, 'onChange'> & {
    width?: number | string;
    min?: number;
    max?: number;
    readOnly?: boolean;
    onChange?: (value: number) => void;
  };

export const RhfStepNumber = <T extends FieldValues>(props: Props<T>) => {
  const {
    name,
    control,
    label,
    width = 48,
    min,
    max,
    disabled,
    readOnly,
    helperText: defaultHelperText,
    onChange,
    ...textFieldProps
  } = props;
  const {
    field: { ref, value, ...field },
    fieldState: { error },
  } = useController<T>({ name, control });

  const schema = React.useMemo(() => {
    let schema = z.number();
    if (min !== undefined) schema = schema.min(min, `${min}より小さな値を入力してください`);
    if (max !== undefined) schema = schema.max(max, `${max}より大きな値を入力してください`);
    return schema;
  }, [min, max]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const raw = event.target.value;
    const next = Number(raw);
    if (!schema.safeParse(next).success) return;
    onChange?.(next);
    field.onChange(next);
  };
  const handleIncrement = () => field.onChange(value + 1);
  const handleDecrement = () => field.onChange(value - 1);
  const helperText = error?.message ?? defaultHelperText;
  return (
    <Stack>
      <Stack direction="row" spacing={1} alignItems="center">
        <FormLabel sx={{ color: (t) => (readOnly ? t.palette.primary.main : undefined), pr: 1 }}>
          {label}
        </FormLabel>
        <IconButton
          color="primary"
          size="small"
          onClick={handleDecrement}
          disabled={disabled || readOnly || value === min}
          sx={{ visibility: readOnly ? 'hidden' : 'visible' }}
        >
          <Remove fontSize="inherit" />
        </IconButton>
        <Box sx={{ width, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          {!readOnly ? (
            <TextField
              variant="outlined"
              {...field}
              {...textFieldProps}
              value={String(value)}
              onChange={handleChange}
              slotProps={{
                input: { ref },
                htmlInput: { readOnly, sx: { textAlign: 'center', p: 1 } },
              }}
              error={Boolean(error)}
              disabled={disabled}
            />
          ) : (
            <Box sx={{ p: 1, mb: '-1px' }}>{value}</Box>
          )}
        </Box>
        <IconButton
          color="primary"
          size="small"
          onClick={handleIncrement}
          disabled={disabled || readOnly || value === max}
          sx={{ visibility: readOnly ? 'hidden' : 'visible' }}
        >
          <Add fontSize="inherit" />
        </IconButton>
      </Stack>
      {helperText && <FormHelperText error={Boolean(error)}>{helperText}</FormHelperText>}
    </Stack>
  );
};
