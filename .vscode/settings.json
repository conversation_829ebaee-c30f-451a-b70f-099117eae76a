{"files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "[typescript][typescriptreact]": {"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "explicit"}}, "[json][yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma", "editor.formatOnSave": true}, "[markdown]": {"editor.defaultFormatter": "yzhang.markdown-all-in-one", "editor.formatOnSave": true}, "markdown.extension.toc.levels": "2..4", "githubIssues.queries": [{"label": "Your \"priority: high\" Issues", "query": "is:open assignee:${user} label:\"priority: high\" repo:${owner}/${repository}"}, {"label": "Today's Issues", "query": "is:open assignee:${user} label:today repo:${owner}/${repository}"}, {"label": "Today's Team Issues", "query": "is:open label:today repo:${owner}/${repository}"}, {"label": "Team's Doing Issues", "query": "is:open label:doing repo:${owner}/${repository}"}, {"label": "My Issues", "query": "is:open assignee:${user} repo:${owner}/${repository}", "groupBy": ["milestone"]}, {"label": "All Issues", "query": "is:open repo:${owner}/${repository}"}], "github.copilot.enable": {"*": false}}