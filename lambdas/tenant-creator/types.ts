import type { TenantInput } from 'models';
import type { ReadyRls } from '../.prisma';

import type { CommonSecrets } from 'common';
import type { funcs } from 'lambda-api';

export type ApiFuncs = typeof funcs;
export type Tenant = Awaited<ReturnType<ApiFuncs['tenant']['get']>>;

export type TenantContext = TenantInput &
  ReadyRls & {
    tenantId: string;
    imageKeys: string[];
    funcs: ApiFuncs;
    secrets: CommonSecrets;
  };
