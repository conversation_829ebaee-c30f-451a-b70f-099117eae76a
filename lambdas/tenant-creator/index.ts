import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import type { <PERSON><PERSON> } from 'aws-lambda';

import { type CommonSecrets, system } from 'common';
import { funcs } from 'lambda-api';

import type { TenantInput } from 'models';
import {
  createBicycleSamples,
  createDefaultsOnDB,
  createEnvClient,
  createRlsClientProvider,
  createSamplesWithoutBicyclesOnDB,
  createSuperClient,
  createTenant,
  putUserPoolJsonToS3,
} from '../.prisma';
import type { TenantContext } from './types';

const fetchCommonSecrets = async () => {
  const secretsManager = new SecretsManagerClient();
  const command = new GetSecretValueCommand({ SecretId: system });
  const { SecretString } = await secretsManager.send(command);
  if (SecretString === undefined) throw new Error('Secret not found');
  return JSON.parse(SecretString) as CommonSecrets;
};

export type TenantCreatorEvent = {
  tenant: TenantInput;
  images: { defaults: string[]; dummies: string[] };
};

export const handler: Handler<TenantCreatorEvent> = async (event) => {
  console.log(`\nEVENT: ${JSON.stringify(event, null, 2)}`);
  const {
    tenant,
    images: { defaults, dummies },
  } = event;

  const prismaClients = { super: createSuperClient(), env: createEnvClient() };
  const rlsClientProvider = createRlsClientProvider();

  // テナントと初期データを作成
  const found = await prismaClients.env.tenant.findUnique({
    where: { subdomain: tenant.subdomain },
  });
  const tenantId = found?.id ?? crypto.randomUUID();
  const readyRls = rlsClientProvider.setTenantId(tenantId);
  const secrets = await fetchCommonSecrets();
  const context: TenantContext = {
    ...readyRls,
    ...tenant,
    tenantId,
    imageKeys: defaults,
    funcs,
    secrets,
  };
  if (!found) await createTenant(context);

  // 以下、テナントの初期データを作成
  await putUserPoolJsonToS3(tenant.subdomain);
  await createDefaultsOnDB(context);
  await createSamplesWithoutBicyclesOnDB(context);
  if (tenant.generateBicycleSamples) await createBicycleSamples(context, dummies);
  return console.log(`Tenant ${tenant.shortName} created successfully.`);
};
