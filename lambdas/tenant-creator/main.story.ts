import { afterAll, it } from 'vitest';

import type { CommonSecrets } from 'common';
import { funcs } from 'lambda-api';
import { publicImageKey } from 'models';
import { shinagawa } from 'models/src/tenants/shinagawa';
import {
  createBicycleSamples,
  createDefaultsOnDB,
  createEnvClient,
  createRlsClientProvider,
  createSamplesWithoutBicyclesOnDB,
  createSuperClient,
  createTenant,
  dropSchemaCascade,
  setupAppUser,
  setupDbSchema,
} from '../.prisma';
import type { TenantContext } from './types';

const tenantId = 'tenantId';

const commonClient = createSuperClient();
const superClient = createEnvClient();
const rlsClientProvider = createRlsClientProvider();
const readyRls = rlsClientProvider.setTenantId(tenantId);

afterAll(async () => {
  await commonClient.$disconnect();
  await superClient.$disconnect();
  await rlsClientProvider.getRawClient().$disconnect();
});

const prepare = async () => {
  await dropSchemaCascade(superClient);
  await setupAppUser(superClient);
  await setupDbSchema(
    {
      super: commonClient,
      env: superClient,
    },
    '../../packages/database/**/migration.sql',
    true,
  );
};

it('story', async () => {
  await prepare();
  const context: TenantContext = {
    ...readyRls,
    ...shinagawa,
    tenantId,
    imageKeys: [publicImageKey('bicycle-zoom-out.jpg')],
    funcs,
    secrets: {
      google_map_api_key: 'google_map_api_key',
      google_map_id: 'google_map_id',
    } as CommonSecrets,
  };
  const dummyImageKeys = [...Array(30).keys()].map((i) => publicImageKey(`image-${i + 1}.jpg`));
  const test = true;
  await createTenant(context, test);
  await createDefaultsOnDB(context, test);
  await createSamplesWithoutBicyclesOnDB(context, test);
  await createBicycleSamples(context, dummyImageKeys);
}, 300_000);
