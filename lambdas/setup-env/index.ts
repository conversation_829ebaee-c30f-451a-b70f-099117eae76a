import { InvokeCommand } from '@aws-sdk/client-lambda';
import { fromLambdaContext } from 'cwl-link';

import type { Handler } from 'aws-lambda';

import { tenantCreatorName } from 'common';
import { tenants } from 'models';
import type { TenantCreatorEvent } from 'tenant-creator';

import { setupDbSchema } from '../.prisma';

import { envKey, lambda, prismaClients } from './init';

import {
  cleanupEnvIfNotTenantExists,
  createCommonImagesIfNotExists,
  createSystemResourcesIfNotExists,
} from './system';

export const handler: Handler = async (event, context) => {
  const link = fromLambdaContext(context);
  await setupDbSchema(prismaClients, '**/migration.sql', false, link);

  await cleanupEnvIfNotTenantExists();

  // システム全体のリソースを作成
  await createSystemResourcesIfNotExists();

  // 画像をS3にアップロード
  const { defaults, dummies } = await createCommonImagesIfNotExists();

  await Promise.all(
    tenants.map(async (sample) => {
      const event: TenantCreatorEvent = { tenant: sample, images: { defaults, dummies } };
      return lambda.send(
        new InvokeCommand({
          FunctionName: tenantCreatorName(envKey),
          InvocationType: 'Event',
          Payload: JSON.stringify(event),
        }),
      );
    }),
  );

  return console.log('Setup env done✨');
};
