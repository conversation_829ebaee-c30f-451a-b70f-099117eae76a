import type { z } from 'zod';

import { getJpFiscalYear } from 'common';
import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';
import { InquiryInputSchema } from './types';

type Input = z.infer<typeof InquiryInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const createInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { asyncTx } = ctx;
  const { id, receivedAt } = input;

  // Filter out any extra fields that shouldn't be in the Prisma create data
  // const cleanInput = {
  //   id: input.id,
  //   receptionRouteId: input.receptionRouteId,
  //   receiverId: input.receiverId,
  //   receivedAt: input.receivedAt,
  //   title: input.title,
  //   description: input.description,
  //   address: input.address,
  //   memo: input.memo,
  //   images: input.images,
  // };

  const data: Prisma.InquiryCreateInput = {
    ...input,
    receptionRoute: { connect: { id: input.receiverId } },
    fiscalYear: receivedAt ? getJpFiscalYear(receivedAt) : null,
    images: { createMany: { data: input.images } },
  };

  await asyncTx(async (tx) => {
    await tx.inquiry.create({ data: { ...data, id } });
    await tx.inquiry.create({ data: { ...data, original: { connect: { id } } } });
  });

  return 'OK';
};

export const createInquiry = (p: TRPCProcedure) =>
  p.input(InquiryInputSchema).mutation(createInquiryMutation);
