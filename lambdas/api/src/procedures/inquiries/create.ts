import type { z } from 'zod';

import { getJpFiscalYear } from 'common';
import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';
import { InquiryInputSchema } from './types';

type Input = z.infer<typeof InquiryInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const createInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { asyncTx } = ctx;
  const { id, receivedAt } = input;
  const data: Prisma.InquiryCreateInput = {
    ...input,
    fiscalYear: receivedAt ? getJpFiscalYear(receivedAt) : null,
    images: { createMany: { data: input.images } },
  };
  await asyncTx(async (tx) => {
    // Create the original inquiry (not a version)
    await tx.inquiry.create({ data: { ...data, id } });
  });

  return 'OK';
};

export const createInquiry = (p: TRPCProcedure) =>
  p.input(InquiryInputSchema).mutation(createInquiryMutation);
