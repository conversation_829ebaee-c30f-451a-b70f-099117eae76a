import { z } from 'zod';

import { BicycleTypeSchema } from 'common';
import type { Context, TRPCProcedure } from '../../../../types';

const InputSchema = z
  .object({
    fees: z.array(z.object({ type: BicycleTypeSchema, fee: z.number().nonnegative() })),
  })
  .strict();
type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateStorageFeeSettingsMutation = async ({
  input,
  ctx: { sequentialTx, tenantId },
}: MutationArgs) => {
  const { fees } = input;
  await sequentialTx((tx) =>
    fees.map(({ type, fee }) =>
      tx.bicycleTypeSetting.update({
        where: { tenantId_type: { tenantId, type } },
        data: { storageFee: fee },
      }),
    ),
  );
  return 'OK';
};

export const updateStorageFeeSettings = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updateStorageFeeSettingsMutation);
