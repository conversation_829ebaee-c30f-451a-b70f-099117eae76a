import { z } from 'zod';

import { BodyFieldSchema } from 'common';

import type { Context, TRPCProcedure } from '../../../../types';
import { updateImageSettings } from '../funcs';
import { ImageSettingsInputSchema } from '../types';

const InputSchema = z
  .object({
    allow: z.boolean(),
    requiredBicycleType: z.boolean(),
    additionalBodyFields: z.array(BodyFieldSchema).optional(),
    image: ImageSettingsInputSchema,
  })
  .strict();
type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateEnsureAbandonedSettingsMutation = async ({
  input,
  ctx: { asyncTx, tenantId },
}: MutationArgs) => {
  const { image, ...data } = input;

  await asyncTx(async (tx) => {
    await tx.ensureAbandonedSettings.update({ where: { tenantId }, data });
    await updateImageSettings(tx, { findId: tenantId }, image);
  });

  return 'OK';
};

export const updateEnsureAbandonedSettings = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updateEnsureAbandonedSettingsMutation);
