import { z } from 'zod';

import { PatrolFlowSchema } from 'common';

import type { Context, TRPCProcedure } from '../../../../types';

const InputSchema = z
  .object({
    flow: PatrolFlowSchema,
  })
  .strict();
type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updatePatrolFlowMutation = async ({
  input,
  ctx: { prisma, tenantId },
}: MutationArgs) => {
  await prisma.patrolSettings.update({
    where: { tenantId },
    data: input,
  });
  return 'OK';
};

export const updatePatrolFlow = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updatePatrolFlowMutation);
