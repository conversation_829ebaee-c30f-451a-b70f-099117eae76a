import type { StrictOmit } from 'ts-essentials';
import { z } from 'zod';

import { BicycleTypeSchema, isNullish } from 'common';
import type { Prisma, Tx } from '../../.prisma';
import type { Context } from './types';

export const OriginalIdSchema = z.string().brand('OriginalId');
export type OriginalId = z.infer<typeof OriginalIdSchema>;
export const LogIdSchema = z.string().brand('LogId');
export type LogId = z.infer<typeof LogIdSchema>;
export type VersioningResult = { originalId: OriginalId; logId: LogId };

type VersioningKey = keyof Prisma.BicycleUncheckedCreateInput extends infer K
  ? K extends 'tenantId' | 'storageId'
    ? never
    : K extends `${string}Id`
      ? K
      : never
  : never;

/** `VersioningKey` に正しく `OriginalId` を設定するための関数 */
export const upsertBicycle = async (
  tx: Tx,
  /** bicycleId */
  id: string,
  data: StrictOmit<Prisma.BicycleUncheckedCreateInput, VersioningKey> & {
    [key in VersioningKey]?: OriginalId;
  },
) => tx.bicycle.upsert({ where: { id }, create: { id, ...data }, update: data });

/** `VersioningKey` に正しく `LogId` を設定するための関数 */
export const createBicycleEvent = async (
  tx: Tx,
  {
    bicycleId,
    userId,
    ...data
  }: StrictOmit<Prisma.BicycleEventUncheckedCreateInput, VersioningKey> & {
    [key in VersioningKey]?: LogId;
  },
) =>
  tx.bicycleEvent.create({
    data: { ...data, bicycleId, userId, eventLog: { create: { bicycleId, userId } } },
  });

export const BicycleLocationInputSchema = z.object({
  landmarkId: z.string(),
  isNoParkingArea: z.boolean(),
  lat: z.number(),
  lng: z.number(),
  accuracy: z.number().nullish(),
  prefecture: z.string().nullish(),
  city: z.string().nullish(),
  town: z.string().nullish(),
  furtherAddress: z.string().nullish(),
});
export type BicycleLocationInput = z.infer<typeof BicycleLocationInputSchema>;

export const versioningLocation = async (
  tx: Tx,
  bicycleId: string,
  data: BicycleLocationInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningLocation');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.locationId;
  if (isNullish(metaId)) {
    const meta = await tx.bicycleLocationMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.bicycleLocation.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const BicycleBodyInputSchema = z.object({
  type: BicycleTypeSchema.nullable(),
  colors: z.array(z.string()),
  hasBasket: z.boolean().nullable(),
  isLocked: z.boolean().nullable(),
  conditions: z.array(z.string()),
  free1: z.string().min(1).nullable(),
  free2: z.string().min(1).nullable(),
  // 自転車
  registrationNumber: z.string().min(1).nullable(),
  registrationNumberPrefectureCode: z.string().min(1).nullable(),
  registrationNumberPoliceCode: z.string().min(1).nullable(),
  registrationNumber1: z.string().min(1).nullable(),
  registrationNumber2: z.string().min(1).nullable(),
  serialNumber: z.string().min(1).nullable(),
  styleId: z.string().uuid().nullable(),
  // 自転車以外
  numberPlate: z.string().min(1).nullable(),
});

// TODO: 以下の項目は numberPlate からパースする
/**
 * ## 用途による区分
 * - 無: 原付や小型二輪自動車(125cc以下)、電動キックボードなど
 * -  1: 二輪自動車（バイク）
 * - 33: 三輪自動車（あるいは牽引車）
 * - 66: 四輪貨物車
 * - 88: 四輪乗用車
 * - 00: 特種用途の自動車
 */
// typeCode: z.string().min(1).nullable(),
// /** e.g. 渋谷区  */
// name: z.string().min(1).nullable(),
// /** e.g. あ */
// objectiveCode: z.string().min(1).nullable(),
// /** e.g. 1234 (ハイフンを含めない) */
// number: z.string().min(1).nullable(),

export type BicycleBodyInput = z.infer<typeof BicycleBodyInputSchema>;

export const bodyInputToData = (
  metaId: string,
  body: BicycleBodyInput,
): Prisma.BicycleBodyUncheckedCreateInput => ({
  ...body,
  metaId,
  lastMeta: { connect: { id: metaId } },
  colors: { createMany: { data: body.colors.map((id) => ({ colorId: id })) } },
  conditions: { createMany: { data: body.conditions.map((id) => ({ conditionId: id })) } },
});

export const versioningBody = async (
  tx: Tx,
  bicycleId: string,
  body: BicycleBodyInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningBody');
    console.log('bicycleId', bicycleId);
    console.log('body', JSON.stringify(body));
  }
  const bicycle = await tx.bicycle.findUnique({
    where: { tenantId, id: bicycleId },
    include: { body: true, owner: { include: { last: true } } },
  });
  console.log('bicycle', bicycle);
  let metaId = bicycle?.body?.id;
  if (isNullish(metaId)) {
    const meta = await tx.bicycleBodyMeta.create({ data: {} });
    metaId = meta.id;
  }
  const data = bodyInputToData(metaId, body);
  const version = await tx.bicycleBody.create({ data });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const TheftReportInputSchema = z.object({
  reported: z.boolean(),
  reportedAt: z.string().nullable(),
});
export type TheftReportInput = z.infer<typeof TheftReportInputSchema>;

export const versioningTheftReport = async (
  tx: Tx,
  bicycleId: string,
  data: TheftReportInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningTheftReport');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.theftReportId;
  if (isNullish(metaId)) {
    const meta = await tx.theftReportMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.theftReport.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const BicycleOwnerInputSchema = z.object({
  name: z.string().nullish(),
  postalCode: z.string().nullish(),
  address: z.string().nullish(),
  tel: z.string().nullish(),
});
export type BicycleOwnerInput = z.infer<typeof BicycleOwnerInputSchema>;

type VersioningOwnerArg = {
  tx: Tx;
  bicycleId: string;
  data: StrictOmit<Prisma.BicycleOwnerUncheckedCreateInput, 'metaId'>;
  ctx: Context;
  updateActive: boolean;
};

export const versioningOwner = async ({
  tx,
  bicycleId,
  data,
  ctx,
  updateActive,
}: VersioningOwnerArg): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningOwner');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.ownerId;

  if (isNullish(metaId)) {
    const meta = await tx.bicycleOwnerMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.bicycleOwner.create({
    data: {
      ...data,
      metaId,
      lastMeta: { connect: { id: metaId } },
      activeMeta: updateActive ? { connect: { id: metaId } } : undefined,
    },
  });

  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const NotificationInputSchema = z.object({
  date: z.date().nullable(),
});
export type NotificationInput = z.infer<typeof NotificationInputSchema>;

export const versioningNotification = async (
  tx: Tx,
  bicycleId: string,
  data: NotificationInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningNotification');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.notificationId;
  if (isNullish(metaId)) {
    const meta = await tx.bicycleNotificationMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.bicycleNotification.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const AnnouncementInputSchema = z.object({
  start: z.date().nullable(),
  end: z.date().nullish(),
});
export type AnnouncementInput = z.infer<typeof AnnouncementInputSchema>;

export const versioningAnnouncement = async (
  tx: Tx,
  bicycleId: string,
  data: AnnouncementInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningAnnouncement');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.announcementId;
  if (isNullish(metaId)) {
    const meta = await tx.bicycleAnnouncementMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.bicycleAnnouncement.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const DeadlineInputSchema = z.object({
  date: z.date().nullable(),
});
export type DeadlineInput = z.infer<typeof DeadlineInputSchema>;

export const versioningDeadline = async (
  tx: Tx,
  bicycleId: string,
  data: DeadlineInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningDeadline');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.deadlineId;
  if (isNullish(metaId)) {
    const meta = await tx.bicycleDeadlineMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.bicycleDeadline.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const AssessmentInputSchema = z.object({
  price: z.number().int().nonnegative().nullable(),
});
export type AssessmentInput = z.infer<typeof AssessmentInputSchema>;

export const versioningAssessment = async (
  tx: Tx,
  bicycleId: string,
  data: AssessmentInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningAssessment');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.assessmentId;
  if (isNullish(metaId)) {
    const meta = await tx.bicycleAssessmentMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.bicycleAssessment.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const versioningSerialTag = async (
  tx: Tx,
  bicycleId: string,
  data: StrictOmit<Prisma.BicycleSerialTagUncheckedCreateInput, 'metaId'>,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningSerialTag');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.serialTagId;
  if (isNullish(metaId)) {
    const meta = await tx.bicycleSerialTagMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.bicycleSerialTag.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const BicycleStorageLocationInputSchema = z.object({
  storageId: z.string(),
  memo: z.string().nullish(),
});
type BicycleStorageLocationInput = z.infer<typeof BicycleStorageLocationInputSchema>;

export const versioningStorageLocation = async (
  tx: Tx,
  bicycleId: string,
  data: BicycleStorageLocationInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningStorageLocation');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.storageLocationId;
  if (isNullish(metaId)) {
    const meta = await tx.bicycleStorageLocationMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.bicycleStorageLocation.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const versioningTeamRef = async (
  tx: Tx,
  bicycleId: string,
  data: Prisma.TeamRefUncheckedCreateInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningTeamRef');
    console.log('bicycleId', bicycleId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUnique({ where: { tenantId, id: bicycleId } });
  let metaId = bicycle?.teamRefId;
  if (isNullish(metaId)) {
    const meta = await tx.teamRefMeta.create({ data: {} });
    metaId = meta.id;
  }
  const version = await tx.teamRef.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
  return { originalId: OriginalIdSchema.parse(metaId), logId: LogIdSchema.parse(version.id) };
};

export const versioningDealer = async (
  tx: Tx,
  dealerId: string | undefined,
  data: Prisma.BicycleDealerUncheckedCreateInput,
  ctx: Context,
): Promise<VersioningResult> => {
  const { log } = ctx;
  if (log) {
    console.log('versioningDealer');
    console.log('dealerId', dealerId);
    console.log('data', JSON.stringify(data));
  }

  if (isNullish(dealerId)) {
    const original = await tx.bicycleDealer.create({ data: { ...data, nameUnique: data.name } });
    const version = await tx.bicycleDealer.create({ data: { ...data, originalId: original.id } });
    return {
      originalId: OriginalIdSchema.parse(original.id),
      logId: LogIdSchema.parse(version.id),
    };
  }
  const original = await tx.bicycleDealer.update({
    data: { ...data, nameUnique: data.name },
    where: { id: dealerId },
  });
  const version = await tx.bicycleDealer.create({ data: { ...data, originalId: original.id } });
  return { originalId: OriginalIdSchema.parse(original.id), logId: LogIdSchema.parse(version.id) };
};

const versioningRegistrationNumberInner = async (
  tx: Tx,
  data: StrictOmit<Prisma.RegistrationNumberUncheckedCreateInput, 'metaId'>,
  ctx: Context,
) => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningRegistrationNumberInner');
    console.log('tenantId', tenantId);
  }
  const current = await tx.registrationNumber.findFirst({
    where: { tenantId, registrationNumber: data.registrationNumber },
  });
  let metaId = current?.metaId;
  if (isNullish(metaId)) {
    const meta = await tx.registrationNumberMeta.create({ data: {} });
    metaId = meta.id;
  }
  await tx.registrationNumber.create({
    data: { ...data, metaId, lastMeta: { connect: { id: metaId } } },
  });
};

export const versioningRegistrationNumber = async (
  tx: Tx,
  bicycleId: string,
  eventId: string,
  data: StrictOmit<Prisma.BicycleOwnerUncheckedCreateInput, 'metaId'>,
  ctx: Context,
) => {
  const { tenantId, log } = ctx;
  if (log) {
    console.log('versioningRegistrationNumber');
    console.log('bicycleId', bicycleId);
    console.log('eventId', eventId);
    console.log('data', JSON.stringify(data));
  }
  const bicycle = await tx.bicycle.findUniqueOrThrow({
    where: { tenantId, id: bicycleId },
    include: { body: { include: { last: true } } },
  });
  const registrationNumber = bicycle.body?.last?.registrationNumber;
  if (isNullish(registrationNumber)) return console.log('registrationNumber is null');
  await versioningRegistrationNumberInner(
    tx,
    {
      tenantId: ctx.tenantId,
      srcEventId: eventId,
      registrationNumber,
      sourceType: data.sourceType,
      name: data.name,
      postalCode: data.postalCode,
      address: data.address,
      tel: data.tel,
    },
    ctx,
  );
};
