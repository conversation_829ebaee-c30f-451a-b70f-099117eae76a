import type { BedrockRuntimeClient } from '@aws-sdk/client-bedrock-runtime';
import type { CognitoIdentityProviderClient } from '@aws-sdk/client-cognito-identity-provider';
import type { LambdaClient } from '@aws-sdk/client-lambda';
import type { S3Client } from '@aws-sdk/client-s3';

import { TRPC_ERROR_CODES_BY_KEY } from '@trpc/server/rpc';
import { type NonNullProps, type UnionToTuple, type bucketName, objectKeys } from 'common';
import type { Prisma, ReadyRls, createRlsClientProvider } from '../../.prisma';
import type { createTRPC } from './trpc';

export type Clients = {
  rlsClientProvider: ReturnType<typeof createRlsClientProvider>;
  s3: S3Client;
  lambda: LambdaClient;
  cognito: CognitoIdentityProviderClient;
  bedrock: BedrockRuntimeClient;
};

export type Context = Omit<Clients, 'rlsClientProvider'> &
  ReadyRls & {
    userId: string;
    tenantId: string;
    Bucket: ReturnType<typeof bucketName>;
    log: boolean;
  };
export type TRPCInstance = ReturnType<typeof createTRPC>;
export type TRPCProcedure = TRPCInstance['procedure'];

export const TRPC_ERROR_CODES = objectKeys(TRPC_ERROR_CODES_BY_KEY) as UnionToTuple<
  keyof typeof TRPC_ERROR_CODES_BY_KEY
>;

export const internalTenantArgs = {
  include: {
    find: true,
    ensureAbandoned: true,
    remove: true,
    notification: true,
    announcement: true,
    deadline: true,
    recycle: true,
  },
} as const satisfies Prisma.TenantDefaultArgs;
export type InternalTenantRaw = Prisma.TenantGetPayload<typeof internalTenantArgs>;
export type InternalTenant = NonNullProps<InternalTenantRaw>;

export const typesOfBicycleListInput = [
  'stored',
  'number',
  'print',
  'police-request',
  'notified',
  'notifiable',
  'returnable',
  'release',
] as const;
export type TypeOfBicycleListInput = (typeof typesOfBicycleListInput)[number];
