import type { EnvKey } from '@/types';
import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import type { Construct } from 'constructs';
import type { TenantInput } from 'models';

/**
# スタックの削除手順について

 */

interface Props extends cdk.StackProps {
  envKey: EnvKey;
  tenant: TenantInput;
}

export class MainStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: Props) {
    super(scope, id, props);
    const { envKey, tenant } = props;
    if (tenant.storageSettings === undefined)
      throw new Error('Tenant storage settings are required.');
    const { ips, users } = tenant.storageSettings;
    const suffix = envKey !== 'stage' ? '' : '-stage';

    // ログバケットの作成
    const logBucketName = `maphin-storage-logs-for-${tenant.id}${suffix}`;
    const logBucket = new s3.Bucket(this, 'log-bucket', {
      bucketName: logBucketName,
      // 本番環境のバケット削除は手動で実施
      removalPolicy: envKey === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: envKey !== 'prod',
      versioned: false,
      publicReadAccess: false,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
    });

    // メインバケットの作成
    const mainBucketName = `maphin-storage-for-${tenant.id}${suffix}` as const;
    const mainBucket = new s3.Bucket(this, 'bucket', {
      bucketName: mainBucketName,
      // 本番環境のバケット削除は手動で実施
      removalPolicy: envKey === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: envKey !== 'prod',
      versioned: true, // バージョニング有効
      publicReadAccess: false,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      serverAccessLogsBucket: logBucket,
      serverAccessLogsPrefix: 'main-bucket-access/',
    });

    // 品川区IP以外からのアクセスを拒否
    mainBucket.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.DENY,
        principals: [new iam.AnyPrincipal()],
        actions: ['s3:GetObject*', 's3:PutObject*', 's3:DeleteObject*', 's3:List*'],
        resources: [mainBucket.bucketArn, `${mainBucket.bucketArn}/*`],
        conditions: {
          NotIpAddress: { 'aws:SourceIp': ips },
        },
      }),
    );

    // S3バケット名を出力
    new cdk.CfnOutput(this, 'bucket-name', {
      value: mainBucket.bucketName,
      description: `${tenant.name}専用ストレージ`,
    });

    const userAccessKeys = users.map((u) => {
      const id = `${u.id}${suffix}`;
      const user = new iam.User(this, `user-${id}`, { userName: id });

      // CloudFormation出力でユーザー情報を表示
      new cdk.CfnOutput(this, `user-name-${id}`, {
        value: user.userName,
        description: `IAM User name (${envKey}) for ${u.name}`,
      });

      // バケット全体への読み書き権限を付与
      mainBucket.grantReadWrite(user);

      // アクセスキーの作成
      const accessKey = new iam.CfnAccessKey(this, `access-key-${id}`, { userName: user.userName });
      return { id, accessKey };
    });

    const secretObjectEntries = userAccessKeys.flatMap(({ id, accessKey }) => [
      [`${id}-accessKeyId`, cdk.SecretValue.unsafePlainText(accessKey.ref)],
      [`${id}-secretAccessKey`, cdk.SecretValue.unsafePlainText(accessKey.attrSecretAccessKey)],
    ]);

    // Secrets Managerにアクセスキー情報を保存
    new secretsmanager.Secret(this, 'user-secrets', {
      secretName: `${tenant.id}-user-secrets${suffix}`,
      description: `Access credentials (${envKey})`,
      secretObjectValue: Object.fromEntries(secretObjectEntries),
    });
  }
}
