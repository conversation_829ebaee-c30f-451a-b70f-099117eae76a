{"name": "cdk-for-storage", "version": "0.1.0", "bin": {"cdk-for-storage": "bin/cdk-for-storage.js"}, "scripts": {"build": "tsc", "cdk": "cdk --profile m-storage", "watch": "tsc -w"}, "dependencies": {"aws-cdk-lib": "2.140.0", "common": "*", "constructs": "10.4.2", "models": "*", "source-map-support": "^0.5.21", "zod": "^3.25.67"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "20.12.7", "aws-cdk": "2.140.0", "tsconfig": "*", "tsx": "^4.7.1", "typescript": "~5.4.5"}}