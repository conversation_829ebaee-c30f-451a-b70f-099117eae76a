#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import z from 'zod';

import { MainStack } from '../lib/main-stack';

import 'source-map-support/register';
import { envKeys } from '@/types';
import { storageTenants } from 'models';

const app = new cdk.App();

// env contextの型定義とパース
const contextEnvSchema = z.object({
  account: z.string(),
  region: z.string(),
});
const env = contextEnvSchema.parse(app.node.tryGetContext('env'));

for (const tenant of storageTenants) {
  for (const envKey of envKeys) {
    const stackId = `maphin-storage-for-${tenant.id}-${envKey}`;
    const stack = new MainStack(app, stackId, { env, tenant, envKey });
    cdk.Tags.of(stack).add('app', 'maphin-storage');
    cdk.Tags.of(stack).add('tenant', tenant.id);
    cdk.Tags.of(stack).add('env', envKey);
    cdk.Tags.of(stack).add('version', '1.0.0');
  }
}
