import { format } from 'date-fns';

import { type EnvKey, MemberSchema, strictEnvKeySchema, system } from 'common';

import { z } from 'zod';
import {
  type AppType,
  appTypes,
  confirmDeploy,
  createTimer,
  existsEnvStack,
  isCodeBuildAsync,
  runner,
  skipConfirm,
} from './common';
import { createEnvFile } from './create-env-file';
import { invokeSetupEnv } from './invoke-setup-env';
import { showUrl } from './show-url';

type DeployFrontArgs = {
  type: AppType;
  envKey: EnvKey;
};

/**
 * ## deployFront
 * apps/web or apps/admin をデプロイします。
 */
export const deployFront = async ({ type, envKey }: DeployFrontArgs) => {
  if (!(await skipConfirm())) await confirmDeploy(envKey, 'S3');

  const { $, echo, chalk } = await import('zx');
  const url = await createEnvFile(type, envKey);

  await runner('Build by vite...', () => $`vite build`);
  echo('Build done✨');

  const flags = ['--delete', '--cache-control', 'max-age=10,public', '--region', 'ap-northeast-1'];
  if (!(await isCodeBuildAsync())) flags.push('--profile', 'maphin');
  const upload = async () => $`aws s3 sync ./dist s3://${system}-${envKey}/apps/${type} ${flags}`;

  await runner(`Upload to pedal-${envKey} S3...`, upload);
  echo('Upload done🎉');

  echo(`${chalk.cyan(url)}\n`);
  return undefined;
};

const envStackNames = (envKey: EnvKey) => [
  `pedal-${envKey}-acm`,
  `pedal-${envKey}-acm-writer`,
  `pedal-${envKey}-waf`,
  `pedal-${envKey}-waf-writer`,
  `pedal-${envKey}-notifier`,
  // `pedal-${envKey}-cd`,
  `pedal-${envKey}-office-file-to-pdf`,
  `pedal-${envKey}`,
];

/**
 * ## deploy
 * リポジトリルートで呼び出され、CDKとフロントを直列デプロイします。
 */
export const deploy = async (envKey: EnvKey) => {
  if (!(await skipConfirm())) await confirmDeploy(envKey, 'CDK & S3');
  const timer = createTimer();
  const { $, spinner, echo, chalk } = await import('zx');

  await spinner('CDK deploy...', async () => {
    await $`npm install`.quiet();
    await $`npm run prisma-gen`.quiet();
    await $`npm run -w packages/cdk cdk deploy pedal-${envKey}`.quiet();
  });
  echo('CDK deploy done✨');

  await spinner('Invoke setup-env lambda...', async () => invokeSetupEnv(envKey));
  echo('Invoke setup-env lambda done✨');

  await spinner(`App(${appTypes.join('/')}) deploy...`, async () => {
    await Promise.all(
      appTypes.map((app) => $`npm run -w apps/${app} deploy ${envKey} -- --auto-approve`.quiet()),
    );
  });
  echo(`App(${appTypes.join('/')}) deploy done✨\n`);
  const duration = timer.stop();
  const stamp = format(new Date(), 'yyyy/MM/dd HH:mm:ss');
  echo(`Deploy done🎉 ${chalk.greenBright(`${stamp} (${duration})`)}`);
  await showUrl(envKey);
};

export const deployAll = async (envKey: EnvKey) => {
  if (!(await skipConfirm())) await confirmDeploy(envKey, 'CDK & S3');
  const timer = createTimer();
  const { $, spinner, echo, chalk } = await import('zx');

  await spinner('CDK deploy...', async () => {
    await $`npm install`.quiet();
    await $`npm run prisma-gen`.quiet();
    const stacks = envStackNames(envKey);
    for (const stack of stacks) await $`npm run -w packages/cdk cdk deploy ${stack}`.quiet();
  });
  echo('CDK deploy done✨');

  await spinner('Invoke setup-env lambda...', async () => invokeSetupEnv(envKey));
  echo('Invoke setup-env lambda done✨');

  await spinner(`App(${appTypes.join('/')}) deploy...`, async () => {
    await Promise.all([
      appTypes.map((app) => $`npm run -w apps/${app} deploy ${envKey} -- --auto-approve`.quiet()),
    ]);
  });
  echo(`App(${appTypes.join('/')}) deploy done✨\n`);
  const duration = timer.stop();
  const stamp = format(new Date(), 'yyyy/MM/dd HH:mm:ss');
  echo(`Deploy all done🎉 ${chalk.greenBright(`${stamp} (${duration})`)}`);
  await showUrl(envKey);
};

/**
 * ## deployHotswap
 * リポジトリルートで呼び出され、CDK(hotswap)とフロントを並列デプロイします。
 * - 初回デプロイでは使えません。
 * - 本番環境、ステージ環境では使えません。
 * - npm install, prisma-gen の実行を省略します。
 */
export const deployHotswap = async (envKey: EnvKey) => {
  const exists = await existsEnvStack(envKey);
  // 初回デプロイはhotswapが使えない
  if (!exists) throw new Error('This is a first deployment. Please use deploy');
  // 本番環境、ステージ環境ではhotswapを禁止する
  const isStrict = strictEnvKeySchema.safeParse(envKey).success;
  if (isStrict) throw new Error('This is a production environment. Please use deploy');

  const timer = createTimer();
  const { $, spinner, echo, chalk } = await import('zx');

  await spinner('Deploy cdk with hotswap...', async () => {
    await $`npm run -w packages/cdk cdk deploy pedal-${envKey} -- --hotswap`.quiet();
    await invokeSetupEnv(envKey);
  });
  const duration = timer.stop();
  const stamp = format(new Date(), 'yyyy/MM/dd HH:mm:ss');
  echo(`Deploy cdk with hotswap done🎉 ${chalk.greenBright(`${stamp} (${duration})`)}`);
  await showUrl(envKey);
};

/**
 * ## deployFrontFromRoot
 * リポジトリルートで呼び出され、apps/web と apps/admin を並列デプロイします。
 * - 初回デプロイでは使えません。
 * - 本番環境、ステージ環境では使えません。
 */
export const deployFrontFromRoot = async (envKey: EnvKey, appType: AppType) => {
  const timer = createTimer();
  const { $, spinner, echo, chalk } = await import('zx');

  await spinner(`Deploy apps/${appType}...`, async () =>
    $`npm run -w apps/${appType} deploy ${envKey}`.quiet(),
  );
  const duration = timer.stop();
  const stamp = format(new Date(), 'yyyy/MM/dd HH:mm:ss');
  echo(`Deploy apps/${appType} done🎉 ${chalk.greenBright(`${stamp} (${duration})`)}`);
  await showUrl(envKey, appType);
};

/**
 * ## deployFromCodePipeline
 * リポジトリルートで呼び出され、CDKによるデプロイ => フロントのデプロイを実行します。
 */
export const deployFromCodePipeline = async (envKey: EnvKey) => {
  const { $, echo } = await import('zx');
  const timer = createTimer();

  echo('CDK deploy ...');
  const stacks = envStackNames(envKey);
  for (const stack of stacks)
    await $`npm run -w packages/cdk cdk-from-code-pipeline deploy ${stack}`;
  echo('CDK deploy done✨');

  echo('Invoke setup-env lambda...');
  await invokeSetupEnv(envKey);
  echo('Invoke setup-env lambda done✨');

  echo(`App(${appTypes.join('/')}) deploy...`);
  for (const app of appTypes) await $`npm run -w apps/${app} deploy-from-code-pipeline ${envKey}`;
  echo(`App(${appTypes.join('/')}) deploy done✨`);

  const duration = timer.stop();
  const stamp = format(new Date(), 'yyyy/MM/dd HH:mm:ss');
  echo(`All deploy done🎉 ${stamp} (${duration})`);
};

export const createEnv = async (newEnvKey: string) => {
  const { fs, path } = await import('zx');
  const membersPath = path.resolve(process.cwd(), 'members.json');
  const membersJson = fs.readFileSync(membersPath, 'utf-8');
  const members = z.array(z.string()).parse(JSON.parse(membersJson));
  if (members.includes(newEnvKey)) throw new Error('既に存在する環境名です');
  fs.writeFileSync(membersPath, JSON.stringify([...members, newEnvKey], null, 2), 'utf-8');
  await deployAll(MemberSchema.parse(newEnvKey));
};
