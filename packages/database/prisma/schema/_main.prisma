datasource db {
  provider = "postgresql"
  url      = env("DB_URL")
}

generator client {
  provider        = "prisma-client-js"
  output          = "../../../../lambdas/.prisma/generated"
  binaryTargets   = ["native", "rhel-openssl-3.0.x"]
  previewFeatures = ["relationJoins"]
}

// enum周りの定義のみ`packages/common`に出力する
generator zod {
  provider                         = "npx zod-prisma-types"
  output                           = "../../../common/src/enum"
  useMultipleFiles                 = false // default is false
  writeBarrelFiles                 = false // default is true
  createInputTypes                 = false // default is true
  createModelTypes                 = false // default is true
  addInputTypeValidation           = false // default is true
  addIncludeType                   = false // default is true
  addSelectType                    = false // default is true
  validateWhereUniqueInput         = false // default is true
  createOptionalDefaultValuesTypes = false // default is false
  createRelationValuesTypes        = false // default is false
  createPartialTypes               = false // default is false
  useDefaultValidators             = false // default is true
  coerceDate                       = false // default is true
  writeNullishInModelTypes         = false // default is false
}

// generator erd {
//   provider = "prisma-erd-generator"
//   output   = "../../../../docs/detail-design/ER_DIAGRAM.md"
// }

model Migration {
  id        String   @id
  success   Boolean
  sql       String
  log       String?
  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
