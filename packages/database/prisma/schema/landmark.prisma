/// 目印となる場所 (e.g. 駅、公園、施設)
model Landmark {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  locations  BicycleLocation[]
  markers    BicycleMarker[]
  serialTags BicycleSerialTag[]
  rules      RemoveRule[]
  parking    Parking[]
  /// 名称
  name       String
  /// コード（整理番号に使用）
  code       Int
  /// 表示順
  sortOrder  Int
  /// 所在地
  address    String
  /// 緯度
  lat        Float?
  /// 経度
  lng        Float?
  /// 写真
  images     LandmarkImage[]

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@unique([tenantId, code])
}

model LandmarkImage {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  landmarkId String
  landmark   Landmark @relation(fields: [landmarkId], references: [id])

  /// 画像を格納したS3キー
  key         String
  /// 説明
  description String
  // 表示順
  sortOrder   Int    @default(0)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model RemoveRule {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // 対象の車両区分 (What)
  bicycleTypes BicycleType[]

  // 保管所 (To)
  storageId String
  storage   Storage @relation(fields: [storageId], references: [id])

  // ランドマーク (From)
  landmarkId String?
  landmark   Landmark? @relation(fields: [landmarkId], references: [id])

  sortOrder Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
