/// テナント
model Tenant {
  id String @id @default(uuid())

  // リレーション
  // 車両系
  users                User[]
  roles                Role[]
  labels               Label[]
  bicycleTypes         BicycleTypeSetting[]
  colors               BicycleColor[]
  conditions           BicycleCondition[]
  landmarks            Landmark[]
  storages             Storage[]
  removeRules          RemoveRule[]
  policeStations       PoliceStation[]
  numberPlateLocations NumberPlateLocation[]
  noPaymentReasons     ReasonForNoStorageFeePayment[]
  styles               BicycleStyle[]
  makers               BicycleMaker[]
  releaseTags          ReleaseTag[]
  priceSuggestions     PriceSuggestion[]
  parkings             Parking[]
  holidays             Holiday[]
  bicycleNames         BicycleName[]

  map             MapSettings?
  patrol          PatrolSettings?
  mark            MarkSettings?
  find            FindSettings?
  ensureAbandoned EnsureAbandonedSettings?
  remove          RemoveSettings?
  numbering       NumberingSettings?
  store           StoreSettings?
  returnToOwner   ReturnToOwnerSettings?
  notification    NotificationSettings?
  announcement    AnnouncementSettings?
  deadline        DeadlineSettings?
  recycle         RecycleSettings?

  // 問い合わせ系
  receptionRoutes ReceptionRoute[]
  urgencies       InquiryUrgency[]

  /// 正式名称
  name       String @unique
  /// 略称
  shortName  String @unique
  /// 郵便番号
  postalCode String
  /// 住所
  address    String

  /// サブドメイン
  subdomain  String @unique
  /// User pool ID
  userPoolId String

  // デザイン
  /// ロゴURL
  logoKey        String?
  /// テーマカラー(プライマリ)
  primaryColor   String            @default("#007096")
  /// テーマカラー(セカンダリ)
  secondaryColor String            @default("#fe8f1d")
  /// 日付フォーマット
  dateFormat     DateFormatPattern @default(YYYY_MM_DD_SLASH)

  defaultPrefecture Prefecture

  defaultPoliceStationPrefectureCode String?
  defaultPoliceStationId             String?        @unique
  defaultPoliceStation               PoliceStation? @relation("DefaultPoliceStation", fields: [defaultPoliceStationId], references: [id])

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

// テナントごと、テーブルごと、フィールドごとのラベルを格納可能
model Label {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  group   String
  key     String
  value   String
  /// テナント管理者に表示するかどうか
  visible Boolean @default(true)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@unique([tenantId, group, key])
}

model MapSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  defaultLatitude  Float
  defaultLongitude Float

  // Google Maps
  /// API Key
  apiKey String
  /// Map ID
  mapId  String

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model PatrolSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  /// 巡回で使用するページ
  flow PatrolFlow

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model MarkSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  image ImageSettings?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model FindSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  image ImageSettings?

  requiredBicycleType  Boolean     @default(true)
  additionalBodyFields BodyField[] @default([])

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model EnsureAbandonedSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  image ImageSettings?

  // 放置確認機能を使用するか
  allow Boolean @default(true)

  requiredBicycleType  Boolean     @default(true)
  additionalBodyFields BodyField[] @default([])

  // 共通
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  deleted         Boolean  @default(false)
  imageSettingsId String?
}

model RemoveSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  image ImageSettings?

  // 最初の発見から撤去が可能になるまでの時間（分）
  minutes Int

  requiredBicycleType  Boolean     @default(true)
  additionalBodyFields BodyField[] @default([])

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model NumberingSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  serialTagPaper     SerialTagPaper      @default(A4_vertical4x6)
  serialNoPartitions SerialNoPartition[] @default([year, month, day, type, storage, landmark])
  serialNoFormat     String              @default("yy/mm/dd typeCode storageCode landmarkCode seq")

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model StoreSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  image ImageSettings?

  // 車体情報入力の後回しを許可するか
  allowEntryBodyInfoLater Boolean @default(true)
  // 自由入力欄を使用するか
  free1                   Boolean @default(true)
  free2                   Boolean @default(true)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model ImageSettings {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  markId            String?                  @unique
  mark              MarkSettings?            @relation(fields: [markId], references: [tenantId])
  findId            String?                  @unique
  find              FindSettings?            @relation(fields: [findId], references: [tenantId])
  ensureAbandonedId String?                  @unique
  ensureAbandoned   EnsureAbandonedSettings? @relation(fields: [ensureAbandonedId], references: [tenantId])
  removeId          String?                  @unique
  remove            RemoveSettings?          @relation(fields: [removeId], references: [tenantId])
  storeId           String?                  @unique
  store             StoreSettings?           @relation(fields: [storeId], references: [tenantId])
  returnToOwnerId   String?                  @unique
  returnToOwner     ReturnToOwnerSettings?   @relation(fields: [returnToOwnerId], references: [tenantId])

  guides ImageGuide[]

  min Int
  max Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model ImageGuide {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  imageSettingsId String
  imageSettings   ImageSettings @relation(fields: [imageSettingsId], references: [id])

  /// 画像を格納したS3キー
  key         String
  /// 説明
  description String
  /// 表示順
  sortOrder   Int    @default(0)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model ReturnToOwnerSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  image ImageSettings?

  allowPrepay Boolean @default(false)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model NotificationSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  deadlineDays Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model AnnouncementSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // 告示の流れ
  flow AnnouncementFlow
  // 告示日数
  days Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model DeadlineSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  days Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model RecycleSettings {
  tenantId String @id @unique @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  days Int @default(180)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleTypeSetting {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  type       BicycleType
  name       String
  code       Int
  storageFee Int
  sortOrder  Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@unique([tenantId, type])
  @@unique([tenantId, code])
}

model ReasonForNoStorageFeePayment {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  events BicycleEvent[]

  /// 名称
  name      String
  sortOrder Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

// enum

// 日付フォーマットは例外として UPPER_SNAKE_CASE で定義
enum DateFormatPattern {
  /// YYYY/MM/DD
  YYYY_MM_DD_SLASH
  /// YY/MM/DD
  YY_MM_DD_SLASH
  /// YYYY-MM-DD
  YYYY_MM_DD_HYPHEN
  /// YY-MM-DD
  YY_MM_DD_HYPHEN
}

enum SerialNoPartition {
  year
  month
  day
  type
  storage
  landmark
}

enum PatrolFlow {
  mark
  find
  noPatrol // 即時撤去
}

enum AnnouncementFlow {
  each
  monthly
}

enum BodyField {
  colors
  hasBasket
  isLocked
  conditions
  style
  free1
  free2
  registrationNumber
  serialNumber
  numberPlate
}

enum SerialTagPaper {
  A4_vertical3x4
  A4_vertical4x6
}
