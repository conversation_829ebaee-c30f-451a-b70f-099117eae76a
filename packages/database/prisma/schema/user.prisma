model User {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  notifications   NotificationsOnUsers[]
  teams           TeamsOnUsers[]
  events          BicycleEvent[]
  markers         BicycleMarker[]
  inquiries       Inquiry[]
  assignInquiries InquiriesOnUsers[]
  inquiryComments InquiryComment[]

  readLogs  BicycleReadLog[]
  eventLogs BicycleEventLog[]

  roleId String
  role   Role   @relation(fields: [roleId], references: [id])

  /// 名前
  name              String  @db.VarChar(100)
  /// 表示名
  displayName       String? @db.VarChar(100)
  displayNameUnique String?
  /// フリガナ
  kana              String? @db.VarChar(100)
  /// メール
  email             String?
  emailUnique       String?
  /// TEL
  tel               String? @db.VarChar(20)
  /// 備考
  memo              String? @db.VarChar(1000)
  /// ログイン可能かどうか
  canLogin          Boolean @default(true)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@unique([tenantId, displayNameUnique])
  @@unique([tenantId, emailUnique])
}

model Role {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  users      User[]
  searchSets SearchSetsOnRoles[]

  notifications Notification[]

  name        String
  description String?
  sortOrder   Int
  features    String[]

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model SearchSet {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  items SearchItem[]
  roles SearchSetsOnRoles[]

  name      String
  sortOrder Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SearchItem {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  setId String
  set   SearchSet @relation(fields: [setId], references: [id])

  field     SearchField
  type      SearchItemType
  sortOrder Int            @default(0)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SearchSetsOnRoles {
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  setId  String
  set    SearchSet @relation(fields: [setId], references: [id])
  roleId String
  role   Role      @relation(fields: [roleId], references: [id])

  sortOrder Int      @default(0)
  updatedAt DateTime @updatedAt

  // 共通
  createdAt DateTime @default(now())

  @@id([tenantId, setId, roleId])
}

model Notification {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  users  NotificationsOnUsers[]
  roleId String?
  role   Role?                  @relation(fields: [roleId], references: [id])
  teamId String?
  team   Team?                  @relation(fields: [teamId], references: [id])

  /// タイトル
  title       String
  /// 本文
  description String
  /// 通知タイプ
  type        NotificationType
  /// 通知対象
  scope       NotificationScope

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model NotificationsOnUsers {
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  notificationId String
  notification   Notification @relation(fields: [notificationId], references: [id])
  userId         String
  user           User         @relation(fields: [userId], references: [id])

  readAt DateTime?
  hide   Boolean   @default(false)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([tenantId, notificationId, userId])
}

model Team {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  marks         BicycleMarker[]
  refs          TeamRef[]
  notifications Notification[]
  users         TeamsOnUsers[]

  /// チーム名
  name        String
  /// チームの説明
  description String
  /// チームロゴ
  logoKey     String?

  // 共通
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  deleted          Boolean            @default(false)
  InquiriesOnTeams InquiriesOnTeams[]
}

model TeamsOnUsers {
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  teamId String
  team   Team   @relation(fields: [teamId], references: [id])
  userId String
  user   User   @relation(fields: [userId], references: [id])

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([tenantId, teamId, userId])
}

// enum

enum SearchField {
  createdAt
  removedAt

  status

  storage
  storageLocationMemo
  serialNo

  bicycleType
  registrationNumber
  serialNumber
  numberPlate

  colors
  hasBasket
  isLocked
  conditions

  landmark
  isNoParkingArea
  address

  isRemovedOrStored

  referenceStatus
  ownerName
  ownerPostalCode
  ownerAddress
  theftReport

  announceStatus

  isReturned
  isCollectedStorageFee

  isReleased
  releaseStatus
  ownerStatus
}

enum SearchItemType {
  text
  switch
  selectSingle
  selectMultiple
  date
  dateAfter
  dateBefore
}

enum NotificationType {
  system
  user
}

enum NotificationScope {
  all
  role
  team
}
