import { z } from 'zod';
import type { BicycleType, Landmark } from '../../../lambdas/.prisma';

/**
 * シーケンス番号を3桁の文字列に変換する
 */
export const padSeqTo3Digits = (seq: number) => {
  const seqString = seq.toString();
  return seqString.padStart(3, '0');
};

/**
 * 日付をyyyy-mm-ddの形式に変換する
 * @returns 日付: yyyy-mm-dd
 */
export const createDateString = (date: Date) => {
  return date.toISOString().split('T')[0].replace(/-/g, '-');
};

/**
 * 標準フォーマットを生成する
 * @returns 標準フォーマット: yy-mm-dd-typeCode-storageCode-landmarkCode-seq3
 * 14-11-2024-bicycle-2-0-1
 */
export const qrCodeBaseFormat = (
  date: Date,
  cycleType: BicycleType,
  storageCode: number,
  landmarkCode: number,
  seq: number,
) => {
  const dateString = createDateString(date);
  return `${dateString}-${cycleType}-${storageCode.toString()}-${landmarkCode.toString()}-${seq}`;
};

/**
 * 読みやすいシーケンス番号を生成する
 * @returns 読みやすいシーケンス番号: yy-mm-dd-landmarkName-seq
 * 例：14-11-20-東京-1
 */
export const createSerialNoReadable = (date: Date, landmarkName: Landmark['name'], seq: number) => {
  const dateString = createDateString(date);
  return `${dateString}-${landmarkName}-${seq}`;
};

/**
 * グループキーを生成する
 * @returns グループキー: yyyy-mm-dd-typeCode-landmarkCode-storageCode
 * 2024-11-14-bicycle-1-0
 */
export const createGroupKey = (
  date: Date,
  cycleType: BicycleType,
  landmarkCode: string | number,
  storageCode: string | number,
) => {
  const dateString = createDateString(date);
  return `${dateString}-${cycleType}-${landmarkCode}-${storageCode}`;
};

export const Header = [
  'No',
  '整理番号',
  '照会内容',
  '郵便番号',
  '氏名',
  '住所',
  '盗難届',
  '届出日',
  '受理番号',
  '回答日',
];

const CSVHeaderSchema = z.object({
  No: z.string(),
  整理番号: z.string(),
  照会内容: z.string(),
  郵便番号: z.string(),
  氏名: z.string(),
  住所: z.string(),
  盗難届: z.string(),
  届出日: z.string(),
  受理番号: z.string(),
  回答日: z.string(),
});

export type CSVHeader = z.infer<typeof CSVHeaderSchema>;
