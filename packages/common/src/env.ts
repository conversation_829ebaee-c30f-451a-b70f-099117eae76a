import { z } from 'zod';
import membersJson from '../../../members.json';

/**
 * この変数は以下の目的でのみ使用されます。
 * - CloudFormationスタック、各リソースの接頭辞
 * - 各リソースの`app`タグの値
 * - EC2アクセスのためにダウンロードするpemファイルの接頭辞
 */
export const system = 'pedal' as const;

export const commonEnvKeys = ['dev', 'stage', 'prod'] as const;
export const commonEnvKeySchema = z.enum(commonEnvKeys);
export type CommonEnvKey = z.infer<typeof commonEnvKeySchema>;

export const strictEnvKeys = ['prod', 'stage'] as const;
export const strictEnvKeySchema = z.enum(strictEnvKeys);
export type StrictEnvKey = z.infer<typeof strictEnvKeySchema>;

// 個人環境のキー
export const MemberSchema = z.string().brand('Member');
export const memberEnvKeys = MemberSchema.array().parse(membersJson);

// TODO: ここでは member + pr の組み合わせを意味するキーを定義するべきだが、
// pedal-common の再デプロイが必要になり開発を進めることが難しくなるため
// このままにしておく
// 本番用 AWS アカウントが発行されたら、以下のように変更する
// - dev環境 => main環境（ブランチ名と一致）
// - memberNetworkKey => developmentNetworkKey
// - member => development (開発環境用NetworkKey)
export const memberNetworkKey = 'member' as const;

// ネットワークは次のような構成です。
// - prod, stage, dev: それぞれ専用のサブネット、セキュリティーグループを用意
// - 開発環境 (member + pr): サブネット、セキュリティーグループを共有
// - 開発環境のネットワークキー(識別子)は、現在 member ですが development に移行予定
export const networkKeys = [...commonEnvKeys, memberNetworkKey] as const;
export const networkKeySchema = z.enum(networkKeys);
export type NetworkKey = z.infer<typeof networkKeySchema>;

export const envKeys = ['public', ...commonEnvKeys, ...memberEnvKeys] as const;
export const envKeySchema = z.enum(envKeys);
export type EnvKey = z.infer<typeof envKeySchema>;

// name関係

export const naming = <T extends string>(envKey: EnvKey, id: T) =>
  `${system}-${envKey}-${id}` as const;

export type Naming<T extends string> = ReturnType<typeof naming<T>>;
export const envName = (envKey: EnvKey) => `${system}-${envKey}` as const;

/** アカウントで一意になるよう命名する */
export const commonNaming = <T extends string>(id: T) => `${system}-${id}` as const;

export const bucketName = (envKey: EnvKey) => envName(envKey);

export const setupAppUserLambdaName = 'setup-app-user';

export const setupEnvLambdaName = (envKey: EnvKey) => naming(envKey, 'setup-env');

export const officeFileToPdfId = 'office-file-to-pdf';
export const officeFileToPdfName = (envKey: EnvKey) => naming(envKey, officeFileToPdfId);

export const tenantCreatorId = 'tenant-creator';
export const tenantCreatorName = (envKey: EnvKey) => naming(envKey, tenantCreatorId);

export const domainNameKey = 'domainName' as const;
export const userPoolsJsonKey = 'userPoolsJson' as const;

export type EnvParams = { envKey: EnvKey; tenantSubdomains: string[] };

export const oecIps = ['*************/32', '*************/32'] as const;

export const commonSecretName = system;
export const clusterSecretName = commonNaming('cluster');

export type CommonSecretKey =
  | 'slack_webhook_url'
  | 'code_connection_arn'
  | 'first_username'
  | 'first_user_password'
  | 'google_map_api_key'
  | 'google_map_id';
export type CommonSecrets = Record<CommonSecretKey, string>;

export const debugCode = 'Notify for debug';
