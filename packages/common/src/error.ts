const errorCodes = [
  'DEALER_NAME_ALREADY_EXISTS',
  'POLICE_STATION_NAME_ALREADY_EXISTS',
  'POSTAL_CODE_ALREADY_EXISTS',
  'DISPLAY_NAME_ALREADY_EXISTS',
  'EMAIL_ALREADY_EXISTS',
  'PARKING_CODE_ALREADY_EXISTS',
  'MAKER_NAME_ALREADY_EXISTS',
  'RELEASE_TAG_CODE_ALREADY_EXISTS',
  'MOTORIZED_BICYCLE_NUMBER_PLATE_CONTACT_ALREADY_CREATED',
  'DLTB_CONTACT_ALREADY_CREATED',
  'BICYCLE_NAME_CODE_ALREADY_EXISTS',
  'COLOR_NAME_ALREADY_EXISTS',
] as const;

export type ErrorCode = (typeof errorCodes)[number];

export const isErrorCode = (code: string): code is ErrorCode =>
  errorCodes.includes(code as ErrorCode);
