import { z } from 'zod';

// 以下のコードで辞書順に整列
// const sorted = [...new Set(pages).values()].sort((a, b) => {
//   const partsA = a.split('/');
//   const partsB = b.split('/');
//   const len = Math.max(partsA.length, partsB.length);
//   for (let i = 1; i < len; i++) {
//     if (!partsA[i]) return -1;
//     if (!partsB[i]) return 1;
//     if (partsA[i] < partsB[i]) return -1;
//     if (partsA[i] > partsB[i]) return 1;
//   }
//   return 0;
// });

/** フロントの[To型](../../../apps/web/src/router/core/types.ts)と同じパスを持つ配列  */
export const pages = [
  '/announcements',
  '/announcements/create',
  '/announcements/edit',
  '/bicycles',
  '/bicycles/$id',
  '/bicycles/$id/history',
  '/bicycles/$id/return',
  '/bicycles/create',
  '/bicycles/ensure-abandoned',
  '/bicycles/find',
  '/bicycles/mark',
  '/bicycles/qrcode/scan',
  '/bicycles/remove',
  '/bicycles/return-to-owner-list',
  '/bicycles/store',
  '/deadlines',
  '/deadlines/create',
  '/deadlines/edit',
  '/dispose-contracts',
  '/dispose-contracts/$id',
  '/dispose-contracts/$id/edit',
  '/dispose-contracts/create',
  '/image-analysis',
  '/inquiries',
  '/inquiries/$id',
  '/inquiries/$id/edit',
  '/inquiries/create',
  '/keep',
  '/keep/recipients',
  '/keep/recipients/$id',
  '/keep/storages/search',
  '/keep/storages/status',
  '/keep/storages/unprocessed',
  '/map',
  '/me',
  '/notifications',
  '/notifications/print',
  '/notifications/edit',
  '/patrol',
  '/police-references',
  '/police-references/$id',
  '/police-references/create',
  '/prints/qrcode',
  '/release',
  '/sell-contracts',
  '/sell-contracts/$id',
  '/sell-contracts/$id/edit',
  '/sell-contracts/create',
  '/serial-tags/numbering',
  '/serial-tags/print',
  '/settings',
  '/settings/bicycle-styles',
  '/settings/bicycle-styles/$id',
  '/settings/bicycle-styles/$id/edit',
  '/settings/bicycle-styles/create',
  '/settings/bicycle-styles/import',
  '/settings/conditions',
  '/settings/conditions/$id',
  '/settings/conditions/$id/edit',
  '/settings/conditions/create',
  '/settings/conditions/import',
  '/settings/dealers',
  '/settings/dealers/$id',
  '/settings/dealers/create',
  '/settings/dealers/import',
  '/settings/dltb-contacts',
  '/settings/dltb-contacts/$id',
  '/settings/dltb-contacts/$id/edit',
  '/settings/dltb-contacts/create',
  '/settings/dltb-contacts/import',
  '/settings/price-suggestions',
  '/settings/price-suggestions/$id',
  '/settings/price-suggestions/create',
  '/settings/price-suggestions/$id/edit',
  '/settings/holidays',
  '/settings/holidays/$id',
  '/settings/holidays/$id/edit',
  '/settings/holidays/create',
  '/settings/holidays/import',
  '/settings/keep',
  '/settings/labels',
  '/settings/landmarks',
  '/settings/landmarks/$id',
  '/settings/landmarks/$id/edit',
  '/settings/landmarks/create',
  '/settings/landmarks/import',
  '/settings/makers',
  '/settings/makers/$id',
  '/settings/makers/$id/edit',
  '/settings/makers/create',
  '/settings/makers/import',
  '/settings/motorized-bicycle-number-plate-contacts',
  '/settings/motorized-bicycle-number-plate-contacts/$id',
  '/settings/motorized-bicycle-number-plate-contacts/$id/edit',
  '/settings/motorized-bicycle-number-plate-contacts/create',
  '/settings/motorized-bicycle-number-plate-contacts/import',
  '/settings/number-plate-locations',
  '/settings/number-plate-locations/$id',
  '/settings/number-plate-locations/$id/edit',
  '/settings/number-plate-locations/create',
  '/settings/number-plate-locations/import',
  '/settings/parkings',
  '/settings/parkings/$id',
  '/settings/parkings/$id/edit',
  '/settings/parkings/create',
  '/settings/parkings/import',
  '/settings/patrol',
  '/settings/police-stations',
  '/settings/police-stations/$id',
  '/settings/police-stations/$id/edit',
  '/settings/police-stations/create',
  '/settings/police-stations/import',
  '/settings/postal-codes',
  '/settings/postal-codes/$id',
  '/settings/postal-codes/create',
  '/settings/registration-number-contacts',
  '/settings/registration-number-contacts/$id',
  '/settings/registration-number-contacts/$id/edit',
  '/settings/registration-number-contacts/create',
  '/settings/registration-number-contacts/import',
  '/settings/release-tags',
  '/settings/release-tags/$id',
  '/settings/release-tags/$id/edit',
  '/settings/release-tags/create',
  '/settings/release-tags/import',
  '/settings/roles',
  '/settings/roles/$id',
  '/settings/roles/$id/edit',
  '/settings/roles/create',
  '/settings/storages',
  '/settings/storages/$id',
  '/settings/storages/$id/edit',
  '/settings/storages/create',
  '/settings/storages/import',
  '/settings/bicycle-names',
  '/settings/bicycle-names/$id',
  '/settings/bicycle-names/$id/edit',
  '/settings/bicycle-names/create',
  '/settings/bicycle-names/import',
  '/settings/colors',
  '/settings/colors/$id',
  '/settings/colors/$id/edit',
  '/settings/colors/create',
  '/settings/ui',
  '/statistics',
  '/statistics/cycle-return-history',
  '/statistics/landmark',
  '/statistics/landmarks',
  '/statistics/map',
  '/statistics/police-references/owners',
  '/statistics/released-cycles',
  '/statistics/remove-to-return-breakdown',
  '/statistics/reports/daily',
  '/statistics/reports/monthly',
  '/statistics/theft-cycles',
  '/teams',
  '/teams/$id',
  '/teams/$id/edit',
  '/teams/create',
  '/transfer-contracts',
  '/transfer-contracts/$id',
  '/transfer-contracts/$id/edit',
  '/transfer-contracts/create',
  '/users',
  '/users/$id',
  '/users/$id/edit',
  '/users/create',
  '/system',
  '/system/access-logs',
] as const;

export const PageSchema = z.enum(pages);

export type Page = z.infer<typeof PageSchema>;

export type NavigationGroup =
  | 'home'
  | 'patrol'
  | 'keep'
  | 'release'
  | 'statistics'
  | 'settings' // 設定メニュー
  | 'system'; // システム管理者メニュー
