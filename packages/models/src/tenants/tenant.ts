import { z } from 'zod';

import { type AnnouncementFlow, ImageSchema, type PatrolFlow, type Prefecture } from 'common';
import type { Prisma } from '../../../../lambdas/.prisma';

export const tenantIdGroupPrefix = 'tenant-id:';
export const tenantGroupName = (tenantId: string) => `${tenantIdGroupPrefix}${tenantId}`;
export const tenantIdFromGroups = (groups: string[] | undefined) => {
  /** e.g. "tenant-id:123e4567-e89b-12d3-a456-426614174000" */
  const found = groups?.find((group) => group.startsWith(tenantIdGroupPrefix));
  if (found === undefined) return undefined;
  const [, tenantId] = found.split(':');
  return tenantId;
};
export const publicTenantS3Key = <T extends string>(tenantSubdomain: string, path: T) =>
  `public/${tenantSubdomain}/${path}` as const;
export const tenantUserPoolS3Key = (tenantSubdomain: string) =>
  publicTenantS3Key(tenantSubdomain, 'user-pool.json');

export const defaultAdminRoleName = '管理者';

export const ImageSettingsSchema = z.object({
  min: z.number(),
  max: z.number(),
  guides: z.array(ImageSchema),
});
export type ImageSettings = z.infer<typeof ImageSettingsSchema>;

type StorageUser = { id: string; name: string };
/**
 * ## テナント情報
 * - idは変更不可
 * - subdomainは変更可能
 *
 * 基本的にid=subdomainで管理されるが
 * 途中でsubdomainを変更出来るように設計すること
 */
export type TenantInput = {
  id: string; // 変更不可
  name: string;
  shortName: string;
  postalCode: string;
  address: string;
  prefecture: Prefecture;
  city: string;
  latlng: [number, number];
  subdomain: string; // 変更可能
  ips?: readonly [string, ...string[]];
  patrolFlow: PatrolFlow;
  announcementFlow: AnnouncementFlow;
  allowEnsureAbandoned: boolean;
  generateBicycleSamples: boolean;
  landmarks: Omit<Prisma.LandmarkUncheckedCreateInput, 'code' | 'sortOrder'>[];
  storages: Omit<Prisma.StorageUncheckedCreateInput, 'code' | 'sortOrder'>[];
  labels: { group: string; key: string; value: string }[];

  // S3バケットの設定
  storageSettings?: {
    ips: readonly [string, ...string[]];
    users: [StorageUser, ...StorageUser[]];
  };
};
