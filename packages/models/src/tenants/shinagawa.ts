import { oecIps } from 'common';
import { defLabel } from '../label';
import type { TenantInput } from './tenant';

const ips = [
  ...oecIps,
  '*************/32',
  '*************/32',
  '*************/32',
  '*************/32',
  '**************/32',
  '*************/32',
  '************/32',
  '***********/32',
] as const;

export const shinagawa = {
  id: 'shinagawa',
  name: '品川区放置自転車対策課',
  shortName: '品川区',
  postalCode: '140-8715',
  address: '品川区広町2-1-36',
  prefecture: '東京都',
  city: '品川区',
  latlng: [35.60938885385388, 139.73021433161438],
  subdomain: 'shinagawa',
  ips,
  patrolFlow: 'find',
  announcementFlow: 'monthly',
  allowEnsureAbandoned: false,
  generateBicycleSamples: false,
  landmarks: [
    {
      name: '大井町駅',
      address: '東京都品川区大井1丁目',
      lat: 35.60759779939183,
      lng: 139.73466992818894,
    },
    {
      name: '西大井駅',
      address: '東京都品川区西大井１丁目３−２',
      lat: 35.60179397512081,
      lng: 139.72178011450646,
    },
    {
      name: '大森駅',
      address: '東京都大田区大森北１丁目６',
      lat: 35.58838186541524,
      lng: 139.72787959126333,
    },
    {
      name: '大崎駅',
      address: '東京都品川区大崎１丁目−２１−４',
      lat: 35.61983380228233,
      lng: 139.72818919932965,
    },
    {
      name: '下神明駅',
      address: '東京都品川区西品川１丁目３０',
      lat: 35.608771077421814,
      lng: 139.72621357018494,
    },
    {
      name: '戸越公園駅',
      address: '東京都品川区戸越５丁目１０',
      lat: 35.6089579999152,
      lng: 139.7183423166411,
    },
    {
      name: '青物横丁駅',
      address: '東京都品川区南品川３丁目１−２０',
      lat: 35.60926911128323,
      lng: 139.74299114934345,
    },
    {
      name: '立会川駅',
      address: '東京都品川区東大井２丁目２４',
      lat: 35.59854564977465,
      lng: 139.73891920480273,
    },
    {
      name: '新馬場駅',
      address: '東京都品川区北品川２丁目１８−１',
      lat: 35.616570044159445,
      lng: 139.7414612699041,
    },
    {
      name: '大井競馬場前駅',
      address: '東京都品川区勝島２丁目２',
      lat: 35.59562692292137,
      lng: 139.7469460102888,
    },
    {
      name: '天王洲アイル駅(りんかい線)',
      address: '東京都品川区東品川２丁目５−１９',
      lat: 35.6204225953589,
      lng: 139.75080513895034,
    },
    {
      name: '天王洲アイル駅(モノレール)',
      address: '東京都品川区２丁目３',
      lat: 35.62297793078657,
      lng: 139.75064428828173,
    },
    {
      name: '品川シーサイド駅',
      address: '東京都品川区東品川４丁目１２−２２',
      lat: 35.609706221961304,
      lng: 139.74972018621784,
    },
    {
      name: '五反田駅',
      address: '東京都品川区東五反田１丁目',
      lat: 35.626176386535676,
      lng: 139.7236133293157,
    },
    {
      name: '目黒駅',
      address: '東京都品川区上大崎２丁目',
      lat: 35.63409891182146,
      lng: 139.71581262070242,
    },
    {
      name: '武蔵小山駅',
      address: '東京都品川区小山３丁目４',
      lat: 35.62047438675115,
      lng: 139.7043717468192,
    },
    {
      name: '西小山駅',
      address: '東京都品川区小山６丁目３−１０',
      lat: 35.61568837915598,
      lng: 139.69886839828007,
    },
    {
      name: '不動前駅',
      address: '東京都品川区西五反田３丁目９−１２',
      lat: 35.625671454572014,
      lng: 139.71355798038488,
    },
    {
      name: '荏原町駅',
      address: '東京都品川区中延５丁目２−１',
      lat: 35.60386471736447,
      lng: 139.70751436732152,
    },
    {
      name: '中延駅',
      address: '東京都品川区東中延２−９−１２',
      lat: 35.60581690642489,
      lng: 139.7127538448276,
    },
    {
      name: '旗の台駅',
      address: '東京都品川区旗の台２丁目１３−１',
      lat: 35.60486965480781,
      lng: 139.70273278495034,
    },
    {
      name: '戸越駅',
      address: '東京都品川区戸越３丁目４−１７',
      lat: 35.61458136700748,
      lng: 139.71640506892174,
    },
    {
      name: '荏原中延駅',
      address: '東京都品川区東中延１丁目９',
      lat: 35.60995740679856,
      lng: 139.71196526854362,
    },
    {
      name: '戸越銀座駅',
      address: '東京都品川区平塚２丁目１６−１',
      lat: 35.61596335697402,
      lng: 139.71496625752692,
    },
  ],
  storages: [
    {
      name: '八潮北保管所',
      address: '東京都品川区八潮１丁目３−１-1',
      lat: 35.611506101497596,
      lng: 139.75483458396508,
    },
    {
      name: '不動前保管所',
      address: '東京都品川区西五反田３丁目１１−１４',
      lat: 35.62757976987274,
      lng: 139.71531114719988,
    },
  ],
  labels: [defLabel('domainAction', 'transfer', '無償譲渡')],
  storageSettings: {
    ips,
    users: [{ id: 'shinagawa-test', name: '品川区テストユーザー' }],
  },
} as const satisfies TenantInput;
