import { SetEx } from 'common';
import { describe, expect, it } from 'vitest';

const parts = [
  'yyyy',
  'yy',
  'mm',
  'dd',
  'typeName',
  'typeCode',
  'landmarkName',
  'landmarkCode',
  'storageName',
  'storageCode',
  'seq',
] as const;
type PartitionPart = (typeof parts)[number];

const dateParts = ['yyyy', 'yy', 'mm', 'dd'];
type DatePart = (typeof dateParts)[number];
const isDatePart = (str: string): str is DatePart => dateParts.includes(str);

const separators = [' ', '-', '/'] as const;
const dateSeparators = ['/', '-'] as const;
type DateSeparatorPart = (typeof dateSeparators)[number];
const isDateSeparator = (str: string): str is DateSeparatorPart => dateSeparators.includes(str);
type SeparatorPart = (typeof separators)[number];

const calcDateSeparators = (parts: string[]): DateSeparatorPart[] => {
  if (parts.length === 1) return [...dateSeparators];
  const [last2, sep, last] = parts.slice(-3);
  if (!isDatePart(last2)) return [...dateSeparators];
  if (!isDateSeparator(sep)) return [...dateSeparators];
  if (!isDatePart(last)) return [...dateSeparators];
  return [sep];
};

const datePartitions = ['year', 'month', 'day'] as const;
const defaultSeparators = [' ', '-'] as const;

const calcSeparators = (partition: string, parts: string[]): SeparatorPart[] => {
  if (datePartitions.includes(partition as any)) return calcDateSeparators(parts);
  return [...defaultSeparators];
};

const nestPart = (part: PartitionPart | undefined): (PartitionPart | SeparatorPart)[] =>
  part ? [part] : [];

const nestParts =
  (parts: (PartitionPart | SeparatorPart)[], sep: SeparatorPart) =>
  (part: PartitionPart | undefined): (PartitionPart | SeparatorPart)[] =>
    part ? [...parts, sep, part] : parts;

const calcSerialNoParts = (
  partitions: string[],
  formatType: 'standard' | 'readable',
): (PartitionPart | SeparatorPart)[][] => {
  const partitionsWithSeq: string[] = [...partitions, 'seq'];
  const partsMap: Record<string, (PartitionPart | undefined)[]> = {
    year: ['yyyy', 'yy'],
    month: ['mm'],
    day: ['dd'],
    type: formatType === 'standard' ? ['typeCode'] : ['typeName', undefined],
    landmark: formatType === 'standard' ? ['landmarkCode'] : ['landmarkName', undefined],
    storage: formatType === 'standard' ? ['storageCode'] : ['storageName', undefined],
    seq: ['seq'],
  };
  const nested = partitionsWithSeq.reduce(
    (array, partition) => {
      const baseParts = partsMap[partition];
      if (array.length === 0) return baseParts.map(nestPart);
      return array.flatMap((parts) => {
        if (parts.length === 0) return baseParts.map(nestPart);
        const seps = calcSeparators(partition, parts);
        return seps.flatMap((sep) => baseParts.map(nestParts(parts, sep)));
      });
    },
    [] as (PartitionPart | SeparatorPart)[][],
  );
  return new SetEx(nested.map((parts) => parts.join('|')))
    .toArray()
    .map((joined) => joined.split('|') as (PartitionPart | SeparatorPart)[])
    .filter((parts) => parts.length > 0);
};

const splitRe = /(?<=\w)(?=[ -/])|(?<=[ -/])(?=\w)/g;

// 以下より元ファイルからのテストコード

describe('calcDateSeparators', () => {
  const parts1: string[] = ['yyyy'];
  it(parts1.join(''), () => expect(calcDateSeparators(parts1)).toEqual(dateSeparators));
  const parts2: string[] = ['yyyy', '/', 'mm'];
  it(parts2.join(''), () => expect(calcDateSeparators(parts2)).toEqual(['/']));
  const parts3: string[] = ['storageCode', '-', 'yyyy'];
  it(parts3.join(''), () => expect(calcDateSeparators(parts3)).toEqual(dateSeparators));
});

describe('calcSerialNoFormats', () => {
  const partitions1: string[] = ['year', 'type'];
  it(`${partitions1.join(', ')}(standard)`, () =>
    expect(calcSerialNoParts(partitions1, 'standard')).toEqual([
      ['yyyy', ' ', 'typeCode', ' ', 'seq'],
      ['yyyy', ' ', 'typeCode', '-', 'seq'],
      ['yyyy', '-', 'typeCode', ' ', 'seq'],
      ['yyyy', '-', 'typeCode', '-', 'seq'],
      ['yy', ' ', 'typeCode', ' ', 'seq'],
      ['yy', ' ', 'typeCode', '-', 'seq'],
      ['yy', '-', 'typeCode', ' ', 'seq'],
      ['yy', '-', 'typeCode', '-', 'seq'],
    ]));
  it(`${partitions1.join(', ')}(readable)`, () =>
    expect(calcSerialNoParts(partitions1, 'readable')).toEqual([
      ['yyyy', ' ', 'typeName', ' ', 'seq'],
      ['yyyy', ' ', 'typeName', '-', 'seq'],
      ['yyyy', ' ', 'seq'],
      ['yyyy', '-', 'seq'],
      ['yyyy', '-', 'typeName', ' ', 'seq'],
      ['yyyy', '-', 'typeName', '-', 'seq'],
      ['yy', ' ', 'typeName', ' ', 'seq'],
      ['yy', ' ', 'typeName', '-', 'seq'],
      ['yy', ' ', 'seq'],
      ['yy', '-', 'seq'],
      ['yy', '-', 'typeName', ' ', 'seq'],
      ['yy', '-', 'typeName', '-', 'seq'],
    ]));
  const partitions2: string[] = ['year', 'month', 'day'];
  it(partitions2.join(', '), () =>
    expect(calcSerialNoParts(partitions2, 'standard')).toEqual([
      ['yyyy', '/', 'mm', '/', 'dd', ' ', 'seq'],
      ['yyyy', '/', 'mm', '/', 'dd', '-', 'seq'],
      ['yyyy', '-', 'mm', '-', 'dd', ' ', 'seq'],
      ['yyyy', '-', 'mm', '-', 'dd', '-', 'seq'],
      ['yy', '/', 'mm', '/', 'dd', ' ', 'seq'],
      ['yy', '/', 'mm', '/', 'dd', '-', 'seq'],
      ['yy', '-', 'mm', '-', 'dd', ' ', 'seq'],
      ['yy', '-', 'mm', '-', 'dd', '-', 'seq'],
    ]));
});

describe('splitRe', () => {
  const serialNo1 = 'yyyy-dd';
  it(serialNo1, () => expect(serialNo1.split(splitRe)).toEqual(['yyyy', '-', 'dd']));
});
