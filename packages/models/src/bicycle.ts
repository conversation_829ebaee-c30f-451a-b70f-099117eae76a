import type { BicycleEventType, BicycleType } from 'common';
import { match } from 'ts-pattern';
import { z } from 'zod';

/** 巡回イベント */
export const patrolEventTypes = [
  'find',
  'ensureAbandoned',
  'remove',
] as const satisfies BicycleEventType[];
export const PatrolEventSchema = z.enum(patrolEventTypes);
export type PatrolEventType = z.infer<typeof PatrolEventSchema>;

export const isPatrolEventType = (type: BicycleEventType): type is PatrolEventType =>
  patrolEventTypes.includes(type);

/** 画像イベント */
export const imageEventTypes = [
  'find',
  'ensureAbandoned',
  'remove',
  'store',
  'editLocation',
  'editBody',
  'editOwner',
] as const satisfies BicycleEventType[];

/** 最終ステータス */
export const finalEventTypes = [
  'ignore',
  'lose',
  'returnToOwner',
  'sell',
  'transfer',
  'dispose',
  'recycle',
  'loseFromStorage',
] as const satisfies BicycleEventType[];

/** 処分イベント ※注意: returnToOwner は含まれません */
export const releaseEventTypes = [
  'sell',
  'transfer',
  'dispose',
  // TODO リサイクル実施してreleasedCycles.tsに適用する
  // 'recycle',
] as const satisfies BicycleEventType[];

export const bicycleOwnerStatuses = ['original', 'municipality'] as const;
export type BicycleOwnerStatus = (typeof bicycleOwnerStatuses)[number];

export const noParkingAreaOptions = ['isNoParkingAreaTrue', 'isNoParkingAreaFalse'] as const;
export type NoParkingAreaOption = (typeof noParkingAreaOptions)[number];

export const hasLicensePlate = (type: BicycleType): boolean =>
  match(type)
    .with('motorizedBicycle' /** 原付 */, () => true)
    .with('motorcycle' /** 自動二輪 */, () => true)
    .with('specifiedSmallMotorizedBicycle' /** 特定小型原動機付自転車 */, () => true)
    .otherwise(() => false);
