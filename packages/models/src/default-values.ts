// TODO: ここにあるコードの大半は models に配置されているメリットがないため移動すべき

import type { BicycleType, SearchField, SearchItemType } from 'common';
import type { Prisma } from '../../../lambdas/.prisma';
import { type Feature, allFeatures } from './role';
import { defaultAdminRoleName } from './tenants/tenant';

const createDefaultRoles = (): Prisma.RoleUncheckedCreateInput[] => {
  const adminFeatures: Feature[] = [];
  const staffFeatures: Feature[] = [];
  const storageAdminFeatures: Feature[] = [];
  const storageStaffFeatures: Feature[] = [];

  // TODO: とりあえずすべてのロールがすべての権限を持っているが、
  // 本来はそれぞれの features を適切に設定する必要がある
  adminFeatures.push(...allFeatures);
  staffFeatures.push(...allFeatures);
  storageAdminFeatures.push(...allFeatures);
  storageStaffFeatures.push(...allFeatures);

  return (
    [
      { name: defaultAdminRoleName, features: adminFeatures },
      { name: '職員', features: staffFeatures },
      { name: '保管所管理者', features: storageAdminFeatures },
      { name: '保管所職員', features: storageStaffFeatures },
    ] as const
  ).map((role, sortOrder) => ({ ...role, sortOrder }));
};

export const defaultRoleInputs = createDefaultRoles();

export const searchFieldTypeMap: Record<SearchField, SearchItemType> = {
  createdAt: 'date',
  removedAt: 'date',

  status: 'selectMultiple',

  storage: 'selectMultiple',
  storageLocationMemo: 'text',
  serialNo: 'text',

  bicycleType: 'selectMultiple',
  registrationNumber: 'text',
  serialNumber: 'text',
  numberPlate: 'text',

  colors: 'selectMultiple',
  hasBasket: 'selectSingle',
  isLocked: 'selectSingle',
  conditions: 'selectMultiple',

  landmark: 'selectMultiple',
  isNoParkingArea: 'selectSingle',
  address: 'text',

  isRemovedOrStored: 'switch',
  referenceStatus: 'selectMultiple',
  ownerName: 'text',
  ownerPostalCode: 'text',
  ownerAddress: 'text',
  theftReport: 'selectSingle',
  announceStatus: 'selectMultiple',
  isReturned: 'switch',
  isCollectedStorageFee: 'switch',
  isReleased: 'switch',
  releaseStatus: 'selectMultiple',
  ownerStatus: 'selectSingle',
};

export const createDefaultSearchSetInputs = (
  tenantId: string,
): Prisma.SearchSetUncheckedCreateInput[] => [
  {
    name: '問い合わせ1',
    sortOrder: 0,
    items: {
      createMany: {
        data: (
          [
            'isRemovedOrStored',
            'removedAt',
            'bicycleType',
            'registrationNumber',
            'serialNumber',
            'numberPlate',
            'colors',
            'hasBasket',
            'isLocked',
            'conditions',
          ] as const satisfies SearchField[]
        ).map((field, i) => ({
          tenantId,
          field,
          type: searchFieldTypeMap[field],
          sortOrder: i + 1,
        })),
      },
    },
  },
  {
    name: '問い合わせ2',
    sortOrder: 1,
    items: {
      createMany: {
        data: (
          [
            'landmark',
            'isNoParkingArea',
            'address',
            'ownerName',
            'ownerPostalCode',
            'ownerAddress',
            'theftReport',
          ] as const satisfies SearchField[]
        ).map((field, i) => ({
          tenantId,
          field,
          type: searchFieldTypeMap[field],
          sortOrder: i + 1,
        })),
      },
    },
  },
  {
    name: '管理その他',
    sortOrder: 2,
    items: {
      createMany: {
        data: (
          [
            'createdAt',
            'storage',
            'serialNo',
            'referenceStatus',
            'announceStatus',
            'isCollectedStorageFee',
            'releaseStatus',
            'ownerStatus',
          ] as const satisfies SearchField[]
        ).map((field, i) => ({
          tenantId,
          field,
          type: searchFieldTypeMap[field],
          sortOrder: i + 1,
        })),
      },
    },
  },
];

export const defaultConditions = ['パンク有り', 'チェーン外れ', 'サドル無し', 'ワイヤーカット済み'];

export const defaultReasonsForNoStorageFeePayment = [
  '盗難届有り',
  '盗難届の確認済み',
  '放置禁止区域外からの移送のため',
  'その他',
];

export const defaultReceptionRoutes = [
  '窓口',
  '電話',
  '問い合わせフォーム',
  'メール',
  'ファックス',
];

export const defaultStorageFees: { type: BicycleType; fee: number }[] = [
  { type: 'bicycle', fee: 3000 },
  { type: 'motorizedBicycle', fee: 5000 },
  { type: 'motorcycle', fee: 5000 },
  { type: 'specifiedSmallMotorizedBicycle', fee: 5000 },
  { type: 'other', fee: 5000 },
];
