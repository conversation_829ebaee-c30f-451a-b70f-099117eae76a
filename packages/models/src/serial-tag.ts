import {
  type BicycleType,
  type SerialNoPartition,
  SetEx,
  getJpFiscalYear,
  isNullish,
} from 'common';
import { format } from 'date-fns';
import type { StrictOmit } from 'ts-essentials';
import { match } from 'ts-pattern';
import { z } from 'zod';
import type { Dict } from './label';

const parts = ['yyyy', 'yy', 'mm', 'dd', 'typeCode', 'landmarkCode', 'storageCode', 'seq'] as const;
type PartitionPart = (typeof parts)[number];
const dateParts = ['yyyy', 'yy', 'mm', 'dd'];
type DatePart = (typeof dateParts)[number];
const isDatePart = (str: string): str is DatePart => dateParts.includes(str);
const separators = [' ', '-', '/'] as const;
const SeparatorSchema = z.enum(separators);
type SeparatorPart = z.infer<typeof SeparatorSchema>;
const defaultSeparators = [' ', '-'] as const;
const dateSeparators = ['/', '-'] as const;
type DateSeparatorPart = (typeof dateSeparators)[number];
const isDateSeparator = (str: string): str is DateSeparatorPart => dateSeparators.includes(str);
const PartSchema = z.enum([...parts, ...separators]);
const calcDateSeparators = (parts: string[]): DateSeparatorPart[] => {
  if (parts.length === 1) return [...dateSeparators];
  const [last2, sep, last] = parts.slice(-3);
  if (!isDatePart(last2)) return [...dateSeparators];
  if (!isDateSeparator(sep)) return [...dateSeparators];
  if (!isDatePart(last)) return [...dateSeparators];
  return [sep];
};

const datePartitions = ['year', 'month', 'day'] as const satisfies SerialNoPartition[];
const calcSeparators = (partition: SerialNoPartition | 'seq', parts: string[]): SeparatorPart[] => {
  if (datePartitions.includes(partition)) return calcDateSeparators(parts);
  return [...defaultSeparators];
};
const nestPart = (part: PartitionPart | undefined): (PartitionPart | SeparatorPart)[] =>
  part ? [part] : [];
const nestParts =
  (parts: (PartitionPart | SeparatorPart)[], sep: SeparatorPart) =>
  (part: PartitionPart | undefined): (PartitionPart | SeparatorPart)[] =>
    part ? [...parts, sep, part] : parts;
const calcSerialNoParts = (
  partitions: SerialNoPartition[],
): (PartitionPart | SeparatorPart)[][] => {
  const partitionsWithSeq: (SerialNoPartition | 'seq')[] = [...partitions, 'seq'];
  const partsMap: Record<SerialNoPartition | 'seq', (PartitionPart | undefined)[]> = {
    year: ['yyyy', 'yy'],
    month: ['mm'],
    day: ['dd'],
    type: ['typeCode'],
    landmark: ['landmarkCode'],
    storage: ['storageCode'],
    seq: ['seq'],
  };
  const nested = partitionsWithSeq.reduce(
    (array, partition) => {
      const baseParts = partsMap[partition];
      if (array.length === 0) return baseParts.map(nestPart);
      return array.flatMap((parts) => {
        if (parts.length === 0) return baseParts.map(nestPart);
        const seps = calcSeparators(partition, parts);
        return seps.flatMap((sep) => baseParts.map(nestParts(parts, sep)));
      });
    },
    [] as (PartitionPart | SeparatorPart)[][],
  );
  return new SetEx(nested.map((parts) => parts.join('|')))
    .toArray()
    .map((joined) => joined.split('|') as (PartitionPart | SeparatorPart)[])
    .filter((parts) => parts.length > 0);
};

const serialNoPartsToOption = (
  parts: (PartitionPart | SeparatorPart)[],
  labels: Dict,
): { value: string; label: string; example: string } => {
  const label = parts
    .map((part) =>
      match(part)
        .with('yyyy', () => '年4桁')
        .with('yy', () => '年2桁')
        .with('mm', () => '月')
        .with('dd', () => '日')
        .with('typeCode', () => '車両区分コード')
        .with('landmarkCode', () => `${labels.domain.landmark}コード`)
        .with('storageCode', () => `${labels.domain.storage}コード`)
        .with('seq', () => '連番')
        .with(' ', () => ' ')
        .with('-', () => ' - ')
        .with('/', () => ' / ')
        .exhaustive(),
    )
    .join('');
  const now = new Date();
  const example = parts
    .map((part) =>
      match(part)
        .with('yyyy', () => format(now, 'yyyy'))
        .with('yy', () => format(now, 'yy'))
        .with('mm', () => format(now, 'MM'))
        .with('dd', () => format(now, 'dd'))
        .with('typeCode', () => '01')
        .with('landmarkCode', () => '000')
        .with('storageCode', () => '00')
        .with('seq', () => '012')
        .with(' ', () => ' ')
        .with('-', () => '-')
        .with('/', () => '/')
        .exhaustive(),
    )
    .join('');
  return { value: parts.join(''), label, example };
};
export const partitionsToOptions = (partitions: SerialNoPartition[], labels: Dict) => {
  const parts = calcSerialNoParts(partitions);
  return parts.map((part) => serialNoPartsToOption(part, labels));
};
type Bicycle = {
  id: string;
  type: BicycleType | null;
  events: { type: string; date: string | Date }[];
  location?: { last: { landmarkId: string | null } | null } | null;
};
type PartitionParams = {
  bicycleId: string;
  /** 年度4桁  */
  fiscalYear: string;
  /** 年4桁  */
  year: string;
  /** 月2桁 */
  month: string;
  /** 日2桁 */
  day: string;
  type: BicycleType;
  landmarkId: string;
  storageId: string;
};
export const parsePartitionParams = (bicycle: Bicycle): Omit<PartitionParams, 'storageId'> => {
  const bicycleId = bicycle.id;
  const event = bicycle.events.find((event) => event.type === 'remove');
  if (!event) throw new Error();
  const fiscalYear = getJpFiscalYear(new Date(event.date)).toString();
  const year = format(new Date(event.date), 'yyyy');
  const month = format(new Date(event.date), 'MM');
  const day = format(new Date(event.date), 'dd');
  const type: BicycleType = 'bicycle'; // TODO: bicycle.body?.type;
  const landmarkId = bicycle.location?.last?.landmarkId;
  if (isNullish(landmarkId)) throw new Error();
  return { bicycleId, fiscalYear, year, month, day, type, landmarkId };
};
export const partitionParamsToKey = (
  partitions: SerialNoPartition[],
  { year, month, day, type, landmarkId, storageId }: PartitionParams,
): string => {
  return partitions
    .map((partition) =>
      match(partition)
        .with('year', () => `year:${year}`)
        .with('month', () => `month:${month}`)
        .with('day', () => `day:${day}`)
        .with('type', () => `type:${type}`)
        .with('landmark', () => `landmark:${landmarkId}`)
        .with('storage', () => `storage:${storageId}`)
        .exhaustive(),
    )
    .join(',');
};
export const calcPartitionKey = (
  partitions: SerialNoPartition[],
  bicycle: Bicycle,
  storageId: string,
) => {
  const params = parsePartitionParams(bicycle);
  return partitionParamsToKey(partitions, { ...params, storageId });
};
const splitRe = /(?<=\w)(?=[ -/])|(?<=[ -/])(?=\w)/g;

type ResourceWithCode = { id: string; name: string; code: number };
export const formatSerialNo = (
  format: string,
  params: StrictOmit<PartitionParams, 'bicycleId'>,
  seq: number,
  typeCodes: { type: BicycleType; name: string; code: number }[],
  landmarks: ResourceWithCode[],
  storages: ResourceWithCode[],
) => {
  const parts = z.array(PartSchema).parse(format.split(splitRe));
  const typeCode = typeCodes.find((t) => t.type === params.type);
  const landmark = landmarks.find((l) => l.id === params.landmarkId);
  const storage = storages.find((s) => s.id === params.storageId);
  if (!typeCode || !landmark || !storage) throw new Error();
  return parts
    .map((part) =>
      match(part)
        .with('yyyy', () => params.year)
        .with('yy', () => params.year.slice(-2))
        .with('mm', () => params.month)
        .with('dd', () => params.day)
        .with('typeCode', () => typeCode.code.toString().padStart(2, '0'))
        .with('landmarkCode', () => landmark.code.toString().padStart(3, '0'))
        .with('storageCode', () => storage.code.toString().padStart(2, '0'))
        .with('seq', () => seq.toString().padStart(3, '0'))
        .with(' ', () => ' ')
        .with('-', () => '-')
        .with('/', () => '/')
        .exhaustive(),
    )
    .join('');
};

export const createSerialNoFormatValidator = (format: string): RegExp => {
  const parts = z.array(PartSchema).parse(format.split(splitRe));
  const stringRegExp = parts
    .map((part) =>
      match(part)
        .with('yyyy', () => '\\d{4}')
        .with('yy', () => '\\d{2}')
        .with('mm', () => '\\d{2}')
        .with('dd', () => '\\d{2}')
        .with('typeCode', () => '\\d{2}')
        .with('landmarkCode', () => '\\d{3}')
        .with('storageCode', () => '\\d{2}')
        .with('seq', () => '\\d{3}')
        .with(' ', () => ' ')
        .with('-', () => '-')
        .with('/', () => '/')
        .exhaustive(),
    )
    .join('');
  return new RegExp(`^${stringRegExp}$`);
};
