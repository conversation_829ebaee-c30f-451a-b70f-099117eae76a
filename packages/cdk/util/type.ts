import type * as cdk from 'aws-cdk-lib';
import { z } from 'zod';

import type { EnvKey, UserPoolName } from 'common';

export const contextEnvSchema = z.object({
  account: z.string(),
  region: z.string(),
});

export type EnvStackProps = cdk.StackProps & {
  envKey: EnvKey;
};

export type UserPool = {
  poolName: UserPoolName;
  userPool: cdk.aws_cognito.UserPool;
  userPoolClient: cdk.aws_cognito.UserPoolClient;
};
