import path from 'node:path';

import type * as sm from 'aws-cdk-lib/aws-secretsmanager';
import type { Construct } from 'constructs';
import { z } from 'zod';

import {
  type EnvKey,
  type NetworkKey,
  commonEnvKeySchema,
  commonNaming,
  memberNetworkKey,
  naming,
  system,
} from 'common';

import { contextEnvSchema } from './type';

export const isProd = (envKey: EnvKey) => envKey === 'prod';
export const rootDir = path.resolve(process.cwd(), '../../');

/** アカウントIDやリージョンの読み込み */
export const envFromContext = (app: Construct) =>
  contextEnvSchema.parse(app.node.tryGetContext('env'));
export const domainNameFromContext = (app: Construct) =>
  z.string().parse(app.node.tryGetContext('domain-name')) as 'maphin.jp';

export const valueFromSecret = <T extends string = string>(
  secret: sm.ISecret | undefined,
  secretKey: T,
): string => {
  if (secret === undefined) {
    // At synth-time, secret is undefined 👻
    return '';
  }
  return secret.secretValueFromJson(secretKey).unsafeUnwrap();
};

export const vpcIdParamName = `/${system}/common/vpc-id` as const;
export const envWebAclArnParamName = (envKey: EnvKey) =>
  `/${system}/${envKey}/waf/web-acl-arn` as const;
export const hostedZoneIdParamName = `/${system}/hosted-zone-id` as const;
export const envCertificateArnParamName = (envKey: EnvKey) =>
  `/${system}/${envKey}/certificate-arn` as const;

export const certificateArnOutputId = 'certificateArn';
export const webAclArnOutputId = 'webAclArn';

export const subnetGroupName = (subnetKey: NetworkKey) => `private-${subnetKey}`;
export const envKeyToNetworkKey = (envKey: EnvKey): NetworkKey => {
  const common = commonEnvKeySchema.safeParse(envKey);
  if (common.success) return common.data;
  return memberNetworkKey;
};
export const envKeyToSubnetGroupName = (envKey: EnvKey) =>
  subnetGroupName(envKeyToNetworkKey(envKey));

export const createLambdaSgName = (subnetKey: NetworkKey) => commonNaming(`${subnetKey}-lambda`);
export const artifactBucketName = commonNaming('artifact-bucket');

export const createDomainName = (app: Construct, envKey: EnvKey) => {
  const origin = domainNameFromContext(app);
  return isProd(envKey) ? origin : (`${envKey}.${origin}` as const);
};
const createAppDomainNames = (app: Construct, envKey: EnvKey, subdomains: string[]) => {
  const domainName = createDomainName(app, envKey);
  return subdomains.map((sub) => `${sub}.${domainName}` as const);
};
export const createDomainNames = (app: Construct, envKey: EnvKey, subdomains: string[]) => {
  const domainName = createDomainName(app, envKey);
  const appDomainNames = createAppDomainNames(app, envKey, subdomains);
  return [domainName, ...appDomainNames];
};

export const notifierName = (envKey: EnvKey) => naming(envKey, 'notifier');
export const s3Name = (envKey: EnvKey) => naming(envKey, 's3');
