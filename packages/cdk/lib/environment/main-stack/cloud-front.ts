import path from 'node:path';

import * as cdk from 'aws-cdk-lib';
import type * as apigateway from 'aws-cdk-lib/aws-apigateway';
import {
  AllowedMethods,
  type BehaviorOptions,
  CachePolicy,
  Distribution,
  FunctionCode,
  FunctionEventType,
  FunctionRuntime,
  type KeyGroup,
  OriginRequestPolicy,
  ResponseHeadersPolicy,
  SecurityPolicyProtocol,
  ViewerProtocolPolicy,
} from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';

import { domainNameKey, envName, naming, s3TenantsPrefix } from 'common';

import { rootDir } from '../../../util/funcs';
import type { EnvStackProps } from '../../../util/type';

import { acmReader } from '../acm-stacks/acm-reader';
import { wafReader } from '../waf-stacks/waf-reader';

const getApiGatewayDomainName = (stack: cdk.Stack, api: apigateway.RestApi) =>
  `${api.restApiId}.execute-api.${stack.region}.${stack.urlSuffix}`;

export type CloudFrontStackProps = EnvStackProps & {
  bucket: cdk.aws_s3.Bucket;
  oai: cdk.aws_cloudfront.OriginAccessIdentity;
  restApi: cdk.aws_apigateway.RestApi;
  keyGroup?: KeyGroup;
  tenantSubdomains: string[];
};

export const createCloudFront = (scope: cdk.Stack, props: CloudFrontStackProps) => {
  const { envKey, bucket, oai, restApi, keyGroup, tenantSubdomains } = props;

  const apiGatewayDomainName = getApiGatewayDomainName(scope, restApi);
  new cdk.CfnOutput(scope, 'ApiGatewayDomainName', { value: apiGatewayDomainName });

  const directoryIndexFunction = new cdk.aws_cloudfront.Function(scope, 'directory-index', {
    functionName: naming(envKey, 'directory-index'),
    code: FunctionCode.fromFile({
      filePath: path.join(rootDir, 'lambdas/directory-index/index.js'),
    }),
    runtime: FunctionRuntime.JS_2_0,
  });

  // 管理オリジンリクエストポリシーの使用
  // https://docs.aws.amazon.com/ja_jp/AmazonCloudFront/latest/DeveloperGuide/using-managed-origin-request-policies.html

  // オリジンリクエストポリシーとキャッシュポリシーの連携方法について
  // https://docs.aws.amazon.com/ja_jp/AmazonCloudFront/latest/DeveloperGuide/understanding-how-origin-request-policies-and-cache-policies-work-together.html

  // 管理レスポンスヘッダーポリシーの使用
  // https://docs.aws.amazon.com/ja_jp/AmazonCloudFront/latest/DeveloperGuide/using-managed-response-headers-policies.html

  const s3OriginProps: Omit<BehaviorOptions, 'origin'> = {
    allowedMethods: AllowedMethods.ALLOW_ALL,
    viewerProtocolPolicy: ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
    originRequestPolicy: OriginRequestPolicy.CORS_S3_ORIGIN,
    cachePolicy: CachePolicy.CACHING_OPTIMIZED,
    responseHeadersPolicy:
      ResponseHeadersPolicy.CORS_ALLOW_ALL_ORIGINS_WITH_PREFLIGHT_AND_SECURITY_HEADERS,
  };
  const tenantBehaviorKey = `${s3TenantsPrefix}/*`;

  const { domainNames, hostedZone, certificate } = acmReader(scope, {
    envKey,
    tenantSubdomains,
  });

  const webAclArn = wafReader(scope, envKey);
  const distribution = new Distribution(scope, 'distribution', {
    domainNames,
    certificate,
    comment: envName(envKey),
    webAclId: webAclArn,
    minimumProtocolVersion: SecurityPolicyProtocol.TLS_V1_2_2021,
    errorResponses: [
      {
        httpStatus: 403,
        responseHttpStatus: 200,
        responsePagePath: '/index.html',
        ttl: cdk.Duration.seconds(10),
      },
    ],
    defaultBehavior: {
      origin: new origins.S3Origin(bucket, {
        originId: naming(envKey, 's3-default'),
        originAccessIdentity: oai,
      }),
      ...s3OriginProps,
      functionAssociations: [
        {
          function: directoryIndexFunction,
          eventType: FunctionEventType.VIEWER_REQUEST,
        },
      ],
    },
    additionalBehaviors: {
      [tenantBehaviorKey]: {
        origin: new origins.S3Origin(bucket, {
          originId: naming(envKey, 'tenants'),
          originAccessIdentity: oai,
        }),
        ...s3OriginProps,
        trustedKeyGroups: keyGroup ? [keyGroup] : undefined,
      },
      'v1/*': {
        origin: new origins.HttpOrigin(apiGatewayDomainName, {
          originId: naming(envKey, 'rest-api'),
        }),
        allowedMethods: AllowedMethods.ALLOW_ALL,
        viewerProtocolPolicy: ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        originRequestPolicy: OriginRequestPolicy.ALL_VIEWER_EXCEPT_HOST_HEADER,
        cachePolicy: new CachePolicy(scope, 'rest-api-cache-policy', {
          cachePolicyName: naming(envKey, 'rest-api'),
          minTtl: cdk.Duration.seconds(0),
          maxTtl: cdk.Duration.seconds(1),
          defaultTtl: cdk.Duration.seconds(1),
        }),
        responseHeadersPolicy:
          ResponseHeadersPolicy.CORS_ALLOW_ALL_ORIGINS_WITH_PREFLIGHT_AND_SECURITY_HEADERS,
      },
    },
  });

  const domainName = domainNames?.at(0) ?? distribution.distributionDomainName;
  new cdk.CfnOutput(scope, domainNameKey, {
    value: domainName,
  });

  const { ARecord, RecordTarget } = cdk.aws_route53;
  const { CloudFrontTarget } = cdk.aws_route53_targets;
  if (domainNames) {
    for (const recordName of domainNames) {
      // https://docs.aws.amazon.com/ja_jp/cdk/v2/guide/identifiers.html
      const id = `a-record-${recordName}`.replace(/\./g, '-');
      new ARecord(scope, id, {
        recordName,
        comment: naming(envKey, 'a-record'),
        zone: hostedZone,
        target: RecordTarget.fromAlias(new CloudFrontTarget(distribution)),
      });
    }
  }

  return {
    domainName,
  };
};
