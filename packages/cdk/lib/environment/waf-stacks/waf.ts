import type { Construct } from 'constructs';

import { createDomainName, webAclArnOutputId } from '@/util/funcs';
import type { EnvStackProps } from '@/util/type';
import * as cdk from 'aws-cdk-lib';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as waf from 'aws-cdk-lib/aws-wafv2';
import { memberEnvKeys, naming, system } from 'common';
import { type TenantInput, tenants } from 'models';

const oec = ['219.59.17.142/32', '126.249.35.14/32'] as const;

const blockedContent = `<html>
<head><meta charset="utf-8"/></head>
<body>
  <div style="position: fixed; top: 50%;left: 50%;transform: translate(-50%, -50%); color: #3C4B65;">
      <h2 style="font-size: 1.75rem;margin-bottom: 0.5rem;font-weight: 500;line-height: 1.2;margin-top: 0;">
          不正なリクエストを検知しました。
      <h2>
      <p style="font-size: 0.875rem;font-weight: 400;line-height: 1.5;text-align: center;color: #3C4B65;">
          心当たりがない場合は管理者までお問い合わせください。
      </p>
  </div>
</body>
</html>` as const;

type Props = EnvStackProps;

export class WafStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: Props) {
    super(scope, id, props);

    const { envKey } = props;

    const ipSetEntries = (tenants as TenantInput[])
      .filter((t) => t.ips)
      .map((t) => {
        if (t.ips === undefined) throw new Error(`Tenant ${t.id} has no IPs defined.`);
        const ipSet = new waf.CfnIPSet(this, `${t.id}-ip-set`, {
          name: naming(envKey, t.id),
          ipAddressVersion: 'IPV4',
          scope: 'CLOUDFRONT',
          addresses: [...t.ips, ...oec],
        });
        return [t.id, ipSet] as const;
      });
    const ipSetMap = new Map<string, waf.CfnIPSet>(ipSetEntries);

    const managedRule = {
      name: naming(envKey, 'managed-rules'),
      priority: 0,
      statement: {
        managedRuleGroupStatement: {
          vendorName: 'AWS',
          name: 'AWSManagedRulesCommonRuleSet',
        },
      },
      overrideAction: { count: {} },
      visibilityConfig: {
        metricName: naming(envKey, 'managed-rules'),
        cloudWatchMetricsEnabled: true,
        sampledRequestsEnabled: true,
      },
    } as const;

    // IPアドレス制限
    const webAcl = new waf.CfnWebACL(this, 'web-acl', {
      name: naming(envKey, 'web-acl'),
      defaultAction: { block: {} },
      scope: 'CLOUDFRONT',
      customResponseBodies: {
        blocked: {
          content: blockedContent,
          contentType: 'TEXT_HTML',
        },
      },
      visibilityConfig: {
        metricName: naming(envKey, 'metric'),
        cloudWatchMetricsEnabled: true,
        sampledRequestsEnabled: true,
      },
      rules: (tenants as TenantInput[]).flatMap(
        (t, index): (cdk.IResolvable | waf.CfnWebACL.RuleProperty)[] => {
          const rules: (cdk.IResolvable | waf.CfnWebACL.RuleProperty)[] = [];

          // OECテナントかつ開発者環境の場合は"AWSManagedRulesCommonRuleSet"を適用しない
          if (t.id !== 'oec' || !memberEnvKeys.includes(envKey)) rules.push(managedRule);

          const ipSet = ipSetMap.get(t.id);
          const priority = 100 + index;
          const action = { allow: {} } as const;
          const commonProps = {
            name: naming(envKey, `${t.id}-ip-filter`),
            priority,
            action,
            visibilityConfig: {
              metricName: `${t.id}-allow-ips`,
              cloudWatchMetricsEnabled: true,
              sampledRequestsEnabled: true,
            },
          };
          const baseDomain = createDomainName(this, envKey);
          const fullDomainName = `${t.subdomain}.${baseDomain}`;
          const byteMatchStatement: cdk.aws_wafv2.CfnWebACL.ByteMatchStatementProperty = {
            fieldToMatch: { singleHeader: { name: 'host' } },
            positionalConstraint: 'EXACTLY',
            searchString: fullDomainName,
            textTransformations: [{ priority: 0, type: 'LOWERCASE' }],
          };
          if (ipSet) {
            // IP制限が設定されているテナントは、サブドメインとIPアドレスの両方でフィルタリング
            rules.push({
              ...commonProps,
              statement: {
                andStatement: {
                  statements: [
                    { byteMatchStatement },
                    { ipSetReferenceStatement: { arn: ipSet.attrArn } },
                  ],
                },
              },
            });
            return rules;
          }
          // IP制限が設定されていないテナントは、サブドメインのみでフィルタリング
          // ※デフォルトアクションがブロックなので
          // サブドメインがマッチしていたらリクエストを通すルールは必須
          rules.push({
            ...commonProps,
            statement: { byteMatchStatement },
          });
          return rules;
        },
      ),
    });

    new cdk.CfnOutput(this, webAclArnOutputId, { value: webAcl.attrArn });

    // WAFログ用CloudWatchロググループ
    const logGroup = new logs.LogGroup(this, 'log-group', {
      // WAFのロググループ名は、"aws-waf-logs-"で始めるという命名規則に従う
      // https://docs.aws.amazon.com/ja_jp/waf/latest/developerguide/logging-cw-logs.html#logging-cw-logs-naming
      logGroupName: `aws-waf-logs-${system}-${envKey}`,
      retention: logs.RetentionDays.ONE_YEAR,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    new cdk.CfnOutput(this, 'log-group-arn', { value: logGroup.logGroupArn });

    // WAFログ出力設定
    const destination = `arn:aws:logs:${this.region}:${this.account}:log-group:${logGroup.logGroupName}`;
    new waf.CfnLoggingConfiguration(this, 'log-config', {
      logDestinationConfigs: [destination],
      resourceArn: webAcl.attrArn,
    });
  }
}
