import * as cdk from 'aws-cdk-lib';
import type { Construct } from 'constructs';

import { artifactBucketName } from '../../util/funcs';

import { createCommonNotifier } from './common-notifier';
import { createDb } from './db';
import { createEc2 } from './ec2';
import { createPublicHostedZone } from './route53';
import { createSetupAppUserLambda } from './setup-app-user-lambda';
import { createVpc } from './vpc';

/**
 * ## WARNING!! ##
 * ## CommonStackの注意点
 *
 * CommonStackでは`vpc.vpcId`をパラメーターストアに保存しています。
 *
 * この値は他のスタックで`cdk.aws_ssm.StringParameter.valueFromLookup`を使用して参照されます。
 * `valueFromLookup`はsynthステップで値を取得して`cdk.context.json`に値を書き込みキャッシュされます。
 * 取得された`vpc.vpcId`は`cdk.aws_ec2.Vpc.fromLookup`に使用されます。
 * `Vpc.fromLookup`も`StringParameter.valueFromLookup`と同様にsynthステップで値を取得して
 * `cdk.context.json`に値を書き込みキャッシュします。
 *
 * スタックの更新によって`vpc.vpcId`が変更されると、`cdk.context.json`に書き込まれた値が古いままになります。
 * 古いままで`vpc.vpcId`が参照できないと当然デプロイは失敗します。
 *
 * 永続的に適用したい context値 はコードに記述しているため、
 * `cdk context --clear`によるキャッシュクリア後にデプロイすることで解決できます。
 *
 * ```sh
 * npm run cdk context -- --clear && npm run cdk deploy pedal-{env}
 * ```
 *
 * 参考リンク: https://docs.aws.amazon.com/ja_jp/cdk/v2/guide/context.html
 */
export class CommonStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: cdk.StackProps) {
    super(scope, id, props);

    // vpc
    /** データベースのポート */
    const port = 5432;
    const { vpc, dbSg, ec2Sg, lambdaSgs } = createVpc(this, port);

    // db
    const { databaseCluster, clusterSecret } = createDb(this, { vpc, dbSg, port });
    // ec2
    createEc2(this, { vpc, ec2Sg });

    const notifier = createCommonNotifier(this);

    const setupAppUserLambda = createSetupAppUserLambda(this, {
      vpc,
      sg: lambdaSgs.dev,
      clusterSecret,
      notifier,
    });
    const { Trigger } = cdk.triggers;
    // 毎回トリガーしたい場合は id に日時情報を含める
    // setup-app-userは初回のみ実行する
    const trigger = new Trigger(this, 'setup-app-user-trigger', { handler: setupAppUserLambda });
    trigger.executeAfter(databaseCluster);

    createPublicHostedZone(this);

    // artifact bucket
    new cdk.aws_s3.Bucket(this, 'artifact-bucket', {
      bucketName: artifactBucketName,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: true,
    });
  }
}
