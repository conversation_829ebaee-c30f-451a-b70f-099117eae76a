import * as cdk from 'aws-cdk-lib';

import { commonNaming, networkKeys, objectFromEntries, oecIps, system } from 'common';

import { createLambdaSgName, subnetGroupName, vpcIdParamName } from '../../util/funcs';

export const createVpc = (
  scope: cdk.Stack,
  /** データベースのポート */
  port: number,
) => {
  // VPC
  const vpc = new cdk.aws_ec2.Vpc(scope, 'vpc', {
    vpcName: system,
    natGateways: 1,
    subnetConfiguration: [
      { cidrMask: 24, name: 'public', subnetType: cdk.aws_ec2.SubnetType.PUBLIC },
      ...networkKeys.map((key) => ({
        cidrMask: 24,
        name: subnetGroupName(key),
        subnetType: cdk.aws_ec2.SubnetType.PRIVATE_WITH_EGRESS,
      })),
    ],
  });

  new cdk.aws_ssm.StringParameter(scope, 'vpc-id', {
    parameterName: vpcIdParamName,
    stringValue: vpc.vpcId,
  });

  // SecurityGroup
  const dbSg = new cdk.aws_ec2.SecurityGroup(scope, 'sg-db', {
    vpc,
    securityGroupName: commonNaming('db'),
  });
  const ec2Sg = new cdk.aws_ec2.SecurityGroup(scope, 'sg-ec2', {
    vpc,
    securityGroupName: commonNaming('ec2'),
    allowAllOutbound: true,
  });
  const lambdaSgs = objectFromEntries(
    networkKeys.map((key) => [
      key,
      new cdk.aws_ec2.SecurityGroup(scope, `sg-lambda-${key}`, {
        vpc,
        securityGroupName: createLambdaSgName(key),
      }),
    ]),
  );

  const tcp5432 = cdk.aws_ec2.Port.tcp(port);
  dbSg.addIngressRule(dbSg, tcp5432, 'Allow DB connection');
  dbSg.addIngressRule(ec2Sg, tcp5432, 'Allow EC2 access DB');
  for (const envKey of networkKeys) {
    dbSg.addIngressRule(lambdaSgs[envKey], tcp5432, `Allow Lambda(${envKey}) access DB`);
  }
  const ips = [...oecIps] as const;
  for (const ip of ips) {
    const ipv4 = cdk.aws_ec2.Peer.ipv4(ip);
    ec2Sg.addIngressRule(ipv4, cdk.aws_ec2.Port.tcp(22), 'Allow SSH Access');
    ec2Sg.addIngressRule(ipv4, cdk.aws_ec2.Port.tcp(80), 'Allow Http Access');
  }

  return { vpc, dbSg, ec2Sg, lambdaSgs };
};
