import * as cdk from 'aws-cdk-lib';

import { clusterSecretName, commonNaming, system } from 'common';

type Props = cdk.StackProps & {
  port: number;
  vpc: cdk.aws_ec2.Vpc;
  dbSg: cdk.aws_ec2.SecurityGroup;
};

export const createDb = (scope: cdk.Stack, props: Props) => {
  const clusterSecret = new cdk.aws_secretsmanager.Secret(scope, 'secret-cluster', {
    secretName: clusterSecretName,
    description: 'Aurora Cluster Secret',
    generateSecretString: {
      includeSpace: false,
      excludePunctuation: true,
      generateStringKey: 'password',
      passwordLength: 30,
      secretStringTemplate: '{"username":"postgres"}',
    },
  });
  const credentials = cdk.aws_rds.Credentials.fromSecret(clusterSecret, 'postgres');
  const storageEncryptionKey = new cdk.aws_kms.Key(scope, 'storage-encryption-key', {
    alias: commonNaming('storage-encryption-key'),
    description: 'storage encryption key for Database Cluster',
    enableKeyRotation: true,
  });

  const { vpc, dbSg } = props;
  const databaseCluster = new cdk.aws_rds.DatabaseCluster(scope, 'cluster', {
    clusterIdentifier: system,
    // ネットワーク／認証
    vpc,
    securityGroups: [dbSg],
    port: 5432,
    credentials,
    storageEncryptionKey,

    // クラスター構成
    engine: cdk.aws_rds.DatabaseClusterEngine.auroraPostgres({
      version: cdk.aws_rds.AuroraPostgresEngineVersion.VER_16_6,
    }),
    serverlessV2MinCapacity: 0.5,
    serverlessV2MaxCapacity: 2,
    writer: cdk.aws_rds.ClusterInstance.serverlessV2('writer', {
      instanceIdentifier: 'serverless-instance',
      caCertificate: cdk.aws_rds.CaCertificate.RDS_CA_RSA4096_G1, // 2121-05-26まで有効
    }),
    readers: [], // readerを定義すると最低１つのreaderインスタンスが生成される

    // メンテナンス／ログ／バックアップ／削除ボリシー
    preferredMaintenanceWindow: 'tue:15:00-tue:16:00', // 日本時間の深夜0:00〜1:00
    cloudwatchLogsExports: ['postgresql'],
    cloudwatchLogsRetention: cdk.aws_logs.RetentionDays.ONE_MONTH,
    backup: {
      preferredWindow: '17:00-18:00', // 日本時間の深夜2:00〜3:00
      retention: cdk.Duration.days(7),
    },
    removalPolicy: cdk.RemovalPolicy.SNAPSHOT,
    copyTagsToSnapshot: true,
  });
  return { databaseCluster, clusterSecret };
};
