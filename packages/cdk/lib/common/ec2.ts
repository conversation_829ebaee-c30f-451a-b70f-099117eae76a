import * as cdk from 'aws-cdk-lib';

import { commonNaming, system } from 'common';

type Props = cdk.StackProps & {
  vpc: cdk.aws_ec2.Vpc;
  ec2Sg: cdk.aws_ec2.SecurityGroup;
};

export const createEc2 = (scope: cdk.Stack, props: Props) => {
  const keyPair = new cdk.aws_ec2.CfnKeyPair(scope, 'key-pair-ec2', {
    keyName: commonNaming('ec2'),
  });
  keyPair.applyRemovalPolicy(cdk.RemovalPolicy.DESTROY);
  const pem = `${system}-ec2.pem` as const;
  new cdk.CfnOutput(scope, '1GetSshKeyCommand', {
    value: [
      'aws ssm get-parameter',
      `--name /ec2/keypair/${keyPair.getAtt('KeyPairId')}`,
      `--region ${scope.region}`,
      '--with-decryption',
      '--query Parameter.Value',
      '--output text',
      '--profile maphin',
      `> ${pem}`,
    ].join(' '),
  });

  const principal = new cdk.aws_iam.ServicePrincipal('ec2.amazonaws.com');
  const policy = cdk.aws_iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore');
  const role = new cdk.aws_iam.Role(scope, 'role-ec2', {
    roleName: commonNaming('ec2'),
    assumedBy: principal,
  });
  role.addManagedPolicy(policy);

  const { vpc, ec2Sg } = props;
  // Ubuntu 22.04 LTS (更新: 2025年6月17日時点の最新LTS)
  const machineImage = new cdk.aws_ec2.GenericLinuxImage({
    'ap-northeast-1': 'ami-00c3b59d617c5e351',
  });
  const ec2 = new cdk.aws_ec2.Instance(scope, 'ec2', {
    instanceName: system,
    machineImage,
    instanceType: cdk.aws_ec2.InstanceType.of(
      cdk.aws_ec2.InstanceClass.T2,
      cdk.aws_ec2.InstanceSize.MICRO,
    ),
    keyName: cdk.Token.asString(keyPair.ref),
    vpc,
    vpcSubnets: { subnetGroupName: 'public' },
    securityGroup: ec2Sg,
    role,
  });

  [
    {
      name: 'chmod',
      command: `chmod 600 ./${pem}`,
    },
    {
      name: 'SshLoginCommand',
      command: `ssh -i ${pem} ubuntu@${ec2.instancePublicIp}`,
    },
    {
      name: 'InstallGuide',
      command: 'https://www.pgadmin.org/download/pgadmin-4-apt/',
    },
  ].forEach(
    ({ name, command }, i) => new cdk.CfnOutput(scope, `${i + 2}${name}`, { value: command }),
  );

  return ec2;
};
