#!/usr/bin/env node

import * as cdk from 'aws-cdk-lib';
import { $, chalk, echo } from 'zx';

import {
  type EnvParams,
  commonNaming,
  envKeys,
  envName,
  naming,
  officeFileToPdfId,
  system,
} from 'common';

import { CommonStack } from '../lib/common/common-stack';
import { SecretStack } from '../lib/common/secret-stack';
import { MainStack } from '../lib/environment/main-stack/main-stack';
import { envFromContext, rootDir } from '../util/funcs';

import { AcmStack } from '@/lib/environment/acm-stacks/acm';
import { AcmWriterStack } from '@/lib/environment/acm-stacks/acm-writer';
import { CdStack } from '@/lib/environment/cd-stack/cd-stack';
import { NotifierStack } from '@/lib/environment/notifier-stack/notifier-stack';
import { OfficeFileToPdfStack as OFPStack } from '@/lib/environment/office-file-to-pdf-stack/office-file-to-pdf-stack';

import 'source-map-support/register';
import { WafStack } from '@/lib/environment/waf-stacks/waf';
import { WafWriterStack } from '@/lib/environment/waf-stacks/waf-writer';
import { tenants } from 'models';

$.cwd = rootDir;

const prepare = async () => {
  echo('Prepare start 🚀\n');

  const target = `lambdas/${officeFileToPdfId}`;
  echo(`Target: ${chalk.cyan(target)}`);

  await $`npm run build -w ${target}`.quiet();
  echo('  Build done');

  // Get tenant subdomains
  const envParams = envKeys.map(
    (envKey): EnvParams => ({
      envKey,
      tenantSubdomains: tenants.map((s) => s.subdomain),
    }),
  );
  echo(
    `  Get all tenant subdomains:
    ${chalk.cyan(
      envParams
        .map(({ envKey, tenantSubdomains }) => `\n    ${envKey}: ${tenantSubdomains.join(', ')}`)
        .join(),
    )}\n`,
  );

  echo('Prepare done 🎉\n');
  return { envParams };
};

const createStacks = (envParams: EnvParams[]) => {
  const app = new cdk.App();
  const env = envFromContext(app);
  const { account, region } = env;
  const contextAzKey = `availability-zones:account=${account}:region=${region}`;
  app.node.setContext(contextAzKey, ['ap-northeast-1a', 'ap-northeast-1c', 'ap-northeast-1d']);
  const props: cdk.StackProps = { env };

  const commonStacks: cdk.Stack[] = [];

  const secretStack = new SecretStack(app, commonNaming('secret'), props);
  commonStacks.push(secretStack);

  const commonStack = new CommonStack(app, commonNaming('common'), props);
  commonStacks.push(commonStack);

  for (const stack of commonStacks) {
    cdk.Tags.of(stack).add('app', system);
    cdk.Tags.of(stack).add('env', 'common');
    cdk.Tags.of(stack).add('version', '1.0.0');
  }

  for (const envKey of envKeys) {
    const envProps = { ...props, envKey };
    const stacks: cdk.Stack[] = [];
    const tenantSubdomains = envParams.find((t) => t.envKey === envKey)?.tenantSubdomains ?? [];

    // Create domain for each environment
    const acmStack = new AcmStack(app, naming(envKey, 'acm'), {
      ...envProps,
      env: { ...props.env, region: 'us-east-1' },
    });
    stacks.push(acmStack);
    stacks.push(
      new AcmWriterStack(app, naming(envKey, 'acm-writer'), {
        ...envProps,
        crossRegionReferences: true,
        acmStack,
      }),
    );
    // Create WAF for each environment
    const wafStack = new WafStack(app, naming(envKey, 'waf'), {
      ...envProps,
      env: { ...props.env, region: 'us-east-1' },
    });
    stacks.push(wafStack);
    stacks.push(
      new WafWriterStack(app, naming(envKey, 'waf-writer'), {
        ...envProps,
        crossRegionReferences: true,
        wafStack,
      }),
    );

    stacks.push(new NotifierStack(app, naming(envKey, 'notifier'), envProps));
    stacks.push(new CdStack(app, naming(envKey, 'cd'), envProps));
    stacks.push(new OFPStack(app, naming(envKey, 'office-file-to-pdf'), envProps));
    stacks.push(new MainStack(app, envName(envKey), { ...envProps, tenantSubdomains }));

    for (const stack of stacks) {
      cdk.Tags.of(stack).add('app', system);
      cdk.Tags.of(stack).add('env', envKey);
      cdk.Tags.of(stack).add('version', '1.0.0');
    }
  }

  app.synth();
};

const main = async () => {
  const { envParams } = await prepare();
  createStacks(envParams);
};
main();
