pre-commit:
  # skip: true
  piped: true
  commands:
    1_install:
      run: npm install
      stage_fixed: true
    2_prisma-gen:
      run: npm run prisma-gen
      stage_fixed: true
    3_check:
      run: npx @biomejs/biome check --write .
      stage_fixed: true
    4_check-spell:
      glob: '*.{ts,tsx,md,json,yaml,yml}'
      exclude:
        - .vscode/settings.json
        - .vscode/extensions.json
        - packages/common/src/map.spec.ts
        - packages/cdk/cdk.json
        - packages/cdk-for-storage/cdk.json
      run: npm run cspell {staged_files}
    5_type:
      run: npm run @type
    6_format:
      glob: '*.{json,yml}'
      run: npx prettier --write {staged_files}
      stage_fixed: true
    7_sort:
      glob: '*package.json'
      run: npx sort-package-json {staged_files}
      stage_fixed: true
