import { Chip, type ChipProps } from '@mui/material';

import type { ReferenceStatus } from 'common';

import {
  referenceStatusToColor,
  referenceStatusToIcon,
  referenceStatusToLabel,
} from '@/models/reference';

type Props = ChipProps & {
  status: ReferenceStatus;
};

export const ReferenceStatusChip = ({ status, ...props }: Props) => {
  const label = referenceStatusToLabel(status);
  const Icon = referenceStatusToIcon(status);
  const color = referenceStatusToColor(status);
  return (
    <Chip
      {...props}
      label={label}
      icon={<Icon style={{ color: color['300'] }} />}
      sx={{ ...props.sx, bgcolor: color['50'] }}
    />
  );
};
