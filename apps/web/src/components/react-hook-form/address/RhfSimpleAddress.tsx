import React from 'react';

import type { Control, UseFormWatch } from 'react-hook-form';

import { PrefectureSchema, isNullish } from 'common';
import type { PostalAddress } from 'lambda-api';
import { extractPrefecture } from 'models';
import { RhfSingleFreeSolo } from 'mui-ex';

import { useAddressList } from './api';

const optionGenerator = (
  address: string | null | undefined,
  prefecture: string | undefined,
  data: PostalAddress[],
): string[] | readonly string[] => {
  if (isNullish(address)) return PrefectureSchema.options;
  if (prefecture === undefined) return PrefectureSchema.options;

  let options = data.map((d) => ({ ...d, option: `${prefecture}${d.city}` }));

  const filledCity = options.some((d) => address.startsWith(d.option));
  if (!filledCity) return Array.from(new Set(options.map((d) => d.option)));

  options = options
    .filter((d) => address.startsWith(d.option) && d.townArea !== '以下に掲載がない場合')
    .map((d) => ({ ...d, option: `${d.option}${d.townArea}` }));

  const town = options.find((d) => address.startsWith(d.option));
  if (!town) return options.map((t) => t.option);

  options = town.chomeList.map((c) => ({ ...town, option: `${town.option}${c}丁目` }));

  const filledChome = options.some((d) => address.startsWith(d.option));
  if (!filledChome) return options.map((t) => t.option);
  return [];
};

type Props = {
  control: Control<any>;
  watch: UseFormWatch<any>;
  name: string;
  readOnly?: boolean;
  label?: string;
  startAdornment?: React.JSX.Element;
  endAdornment?: React.JSX.Element;
};

export const RhfSimpleAddress = (props: Props) => {
  const { control, watch, name, readOnly, label = '住所', startAdornment, endAdornment } = props;
  const address: string | null | undefined = watch(name);
  console.log(
    '%c 📖: RhfSimpleAddress -> address ',
    'font-size:16px;background-color:#97d481;color:black;',
    address,
  );

  React.useEffect( () =)

  const prefecture = extractPrefecture(address);
  const { data = [], isLoading } = useAddressList(prefecture);
  const options = optionGenerator(address, prefecture, data);
  return (
    <RhfSingleFreeSolo
      control={control}
      name={name}
      label={label}
      options={options}
      autoOpen
      readOnly={readOnly}
      loading={isLoading}
      fullWidth
      startAdornment={startAdornment}
      endAdornment={endAdornment}
    />
  );
};
