import { useParams } from '@tanstack/react-router';

import { trpc } from '@/api';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { inquiryRouters } from '@/router/routes/inquiry';
import InquiryDetails from './InquiryDetails';

export default function InquiryDetailsPage() {
  const { id } = useParams({ from: '/app/inquiries/$id' });
  const { data: inquiry } = trpc.inquiries.get.useQuery({ id });

  const title = inquiryRouters.meta.details.useTitle();

  const content = inquiry === undefined ? <LoadingSpinner /> : <InquiryDetails inquiry={inquiry} />;

  return (
    <MainLayout scrollable back={{ to: '/inquiries' }} title={title}>
      {content}
    </MainLayout>
  );
}
