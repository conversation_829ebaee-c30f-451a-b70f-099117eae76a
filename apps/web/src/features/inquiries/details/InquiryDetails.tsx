import { RouterButton } from '@/components/RouterButton';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Edit } from '@mui/icons-material';
import { Box, Paper, Stack } from '@mui/material';
import type { Inquiry } from 'lambda-api';
import { useForm } from 'react-hook-form';
import {
  type InquiryState,
  InquiryStateSchema,
  RhfInquiry,
  inquiryToState,
} from '../common/RhfInquiry';

type Props = {
  inquiry: Inquiry;
};

export default function InquiryDetails({ inquiry }: Props) {
  const { tenant, labels } = useAppContext();
  const { control, watch } = useForm<InquiryState>({
    defaultValues: inquiryToState(inquiry, tenant),
    resolver: zodResolver(InquiryStateSchema),
  });

  return (
    <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
      <RhfInquiry control={control} watch={watch} readOnly />
      <Stack alignItems="end">
        <Box>
          <RouterButton to="/inquiries/$id/edit" params={{ id: inquiry.id }} startIcon={<Edit />}>
            {labels.action.edit}
          </RouterButton>
        </Box>
      </Stack>
    </Stack>
  );
}
