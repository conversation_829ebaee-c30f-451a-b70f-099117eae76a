import { useAppContext } from '@/contexts/app-context';

import KeyValueListItem from '@/components/KeyValueListItem';
import ProfileCard from './ProfileCard';

export default function GeneralCard() {
  const { user, labels } = useAppContext();
  return (
    <ProfileCard title="基本情報">
      <>
        <KeyValueListItem label={labels.system.displayName} value={user.displayName} divider />
        <KeyValueListItem label={labels.system.name} value={user.name} divider />
        <KeyValueListItem label={labels.system.kana} value={user.kana} divider />
        <KeyValueListItem label={labels.contact.tel} value={user.tel} />
      </>
    </ProfileCard>
  );
}
