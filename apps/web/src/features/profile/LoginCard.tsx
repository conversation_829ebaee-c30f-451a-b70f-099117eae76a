import { updatePassword } from '@aws-amplify/auth';
import { zodResolver } from '@hookform/resolvers/zod';
import { HelpOutline } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import {
  Alert,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { enqueueSnackbar } from 'notistack';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import KeyValueListItem from '@/components/KeyValueListItem';
import { useAppContext } from '@/contexts/app-context';

import { RhfTextField } from 'mui-ex';

import ProfileCard from './ProfileCard';

const errorToMessage = (error: Error | null) => {
  if (error === null) return undefined;
  console.log(error.name);
  switch (error.name) {
    case 'NotAuthorizedException':
      return '古いパスワードが正しくありません。パスワードを確認してください。';
    case 'LimitExceededException':
      return 'パスワード変更のリクエストが制限を超えました。しばらくしてから再度お試しください。';
    default:
      return 'エラーが発生しました。';
  }
};

// パスワード変更の実装方法
// https://docs.amplify.aws/gen1/javascript/build-a-backend/auth/manage-passwords/#update-password

const stateSchema = z
  .object({
    oldPassword: z.string().min(1, '古いパスワードを入力してください'),
    // パスワードポリシー
    newPassword: z
      .string()
      .min(8, '新しいパスワードは8文字以上である必要があります')
      .regex(/[A-Z]/, '新しいパスワードには半角の英大文字を含める必要があります')
      .regex(/[a-z]/, '新しいパスワードには半角の英小文字を含める必要があります')
      .regex(/[0-9]/, '新しいパスワードには数字を含める必要があります'),
    newConfirmPassword: z.string().min(1, '新しいパスワード(確認)を入力してください'),
  })
  .superRefine((arg, ctx) => {
    if (arg.newPassword !== arg.newConfirmPassword)
      ctx.addIssue({
        path: ['newConfirmPassword'],
        code: z.ZodIssueCode.custom,
        message: '新しいパスワードが一致しません',
      });
  });
type State = z.infer<typeof stateSchema>;

const defaultValues: State = { oldPassword: '', newPassword: '', newConfirmPassword: '' };

export default function LoginCard() {
  const { labels, user } = useAppContext();
  const [dialog, setDialog] = React.useState(false);
  const {
    control,
    formState: { isSubmitting },
    handleSubmit,
    reset,
  } = useForm<State>({
    defaultValues,
    resolver: zodResolver(stateSchema),
  });

  const { mutate, isError, error } = useMutation({
    mutationFn: async ({ oldPassword, newPassword }: State) =>
      updatePassword({ oldPassword, newPassword }),
    onSuccess: () => {
      reset(defaultValues);
      setDialog(false);
      enqueueSnackbar('パスワードを更新しました。', { variant: 'success' });
    },
  });

  const submit = async (data: State) => mutate(data);

  const errorMessage = errorToMessage(error);

  return (
    <ProfileCard title="ログイン情報">
      <>
        <KeyValueListItem label={labels.contact.email} value={user.email} divider />
        <KeyValueListItem label="パスワード" value="********" onClick={() => setDialog(true)} />
        <Dialog open={dialog} maxWidth="sm" fullWidth onClose={() => setDialog(false)}>
          <DialogTitle>新しいパスワードを設定</DialogTitle>
          <form onSubmit={handleSubmit(submit)}>
            <DialogContent>
              <Stack spacing={2}>
                <DialogContentText>
                  新しいパスワードを入力してください
                  <Tooltip
                    title={
                      <Box>
                        <Typography color="inherit">パスワードポリシー</Typography>
                        <Typography variant="body2">※ 文字数は8文字以上とする</Typography>
                        <Typography variant="body2">※ 半角の英大文字を含める</Typography>
                        <Typography variant="body2">※ 半角の英小文字を含める</Typography>
                        <Typography variant="body2">※ 数字を含める</Typography>
                      </Box>
                    }
                    enterTouchDelay={0}
                    leaveTouchDelay={5000}
                    placement="right"
                  >
                    <IconButton>
                      <HelpOutline />
                    </IconButton>
                  </Tooltip>
                </DialogContentText>
                <RhfTextField
                  control={control}
                  name="oldPassword"
                  type="password"
                  label="古いパスワード"
                  autoFocus
                  fullWidth
                  margin="dense"
                />
                <RhfTextField
                  control={control}
                  name="newPassword"
                  type="password"
                  label="新しいパスワード"
                  fullWidth
                  margin="dense"
                />
                <RhfTextField
                  control={control}
                  name="newConfirmPassword"
                  type="password"
                  label="新しいパスワード(確認)"
                  fullWidth
                  margin="dense"
                />

                {isError && <Alert severity="error">{errorMessage}</Alert>}
              </Stack>
            </DialogContent>
            <DialogActions>
              <LoadingButton loading={isSubmitting} type="submit" variant="contained">
                パスワードを変更する
              </LoadingButton>
            </DialogActions>
          </form>
        </Dialog>
      </>
    </ProfileCard>
  );
}
