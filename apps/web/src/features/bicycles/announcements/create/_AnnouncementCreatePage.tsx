import {
  Alert,
  Box,
  FormControl,
  FormControlLabel,
  Paper,
  Stack,
  Switch,
  type Theme,
  ThemeProvider,
} from '@mui/material';
import { enqueueSnackbar } from 'notistack';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { zodResolver } from '@hookform/resolvers/zod';
import { Add, Close } from '@mui/icons-material';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';

import {
  announcementStartCol,
  announcementStatusCol,
  useBaseBicycleColumns,
} from '@/components/bicycles/columns';
import { RhfBicycleDataGrid } from '@/components/bicycles/react-hook-form/RhfBicycleDataGrid';
import { useAppContext } from '@/contexts/app-context';
import { createUseColumns } from '@/hooks/useColumns';
import { announcementCreate } from '@/router/routes/keep/announcements';
import { stringOrNull } from 'common';
import { startOfDay } from 'date-fns';
import { RhfDatePicker } from 'mui-ex';
import React from 'react';
import { z } from 'zod';

const StateSchema = z.object({
  date: z.date(),
  bicycleIds: z.array(z.string()).min(1),
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

const useAdditionalColumns = createUseColumns([announcementStatusCol, announcementStartCol]);

export const AnnouncementCreatePage = () => {
  const { labels } = useAppContext();
  const title = announcementCreate.meta.useTitle();
  const { control, handleSubmit, watch, formState } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      date: startOfDay(new Date()),
      bicycleIds: [],
      memo: '',
    } as const satisfies State,
    resolver: zodResolver(StateSchema),
  });

  const { data: bicycles = [] } = trpc.bicycles.list.useQuery({ type: 'stored' });
  const { error, mutateAsync } = trpc.bicycles.announcement.start.useMutation({
    onSuccess: () => {
      navigate({ to: '/announcements' });
      enqueueSnackbar(`新規の${labels.da.announce}を${labels.action.create}しました`, {
        variant: 'success',
      });
    },
  });

  const navigate = useNavigate();
  const submit = async (state: State) =>
    mutateAsync({
      start: state.date,
      bicycleIds: state.bicycleIds,
      memo: stringOrNull(state.memo),
    });

  const [filter, setFilter] = React.useState(false);

  const bicycleIds = watch('bicycleIds');
  const rows = filter ? bicycles.filter((b) => bicycleIds.includes(b.id)) : bicycles;
  const baseColumns = useBaseBicycleColumns(bicycles);
  const additionalColumns = useAdditionalColumns(bicycles);
  const columns = [...baseColumns, ...additionalColumns];

  return (
    <MainLayout title={title}>
      <Stack component="form" noValidate onSubmit={handleSubmit(submit)} sx={{ height: 1 }}>
        <Stack component={Paper} spacing={2} sx={{ p: 2, height: 1 }}>
          <Stack spacing={2} sx={{ flexGrow: 1, overflow: 'auto' }}>
            <Box>
              <ThemeProvider<Theme>
                theme={(t) => ({
                  ...t,
                  components: { ...t.components, MuiButton: { defaultProps: { variant: 'text' } } },
                })}
              >
                <RhfDatePicker control={control} name="date" label={`${labels.da.announce}日`} />
              </ThemeProvider>
            </Box>
            <Stack sx={{ flexGrow: 1, overflow: 'auto' }}>
              <RhfBicycleDataGrid
                control={control}
                columns={columns}
                rows={rows}
                hideFooterSelectedRowCount
              />
            </Stack>
            <FormControl fullWidth>
              <FormControlLabel
                label={`選択した${labels.domain.bicycle}のみを表示する`}
                control={
                  <Switch
                    onChange={(_, checked) => setFilter(checked)}
                    checked={filter}
                    disabled={bicycleIds.length === 0}
                  />
                }
              />
            </FormControl>
          </Stack>
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
          <Stack alignItems="end" sx={{ mt: 2 }}>
            <TwoActionButtons
              primary={{
                type: 'submit',
                icon: Add,
                label: labels.doing.create,
                loading: formState.isSubmitting,
              }}
              secondary={{
                icon: Close,
                end: true,
                label: labels.doing.cancel,
                onClick: () => navigate({ to: '/announcements' }),
              }}
            />
          </Stack>
        </Stack>
      </Stack>
    </MainLayout>
  );
};
