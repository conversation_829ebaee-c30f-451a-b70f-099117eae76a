import { Button, Paper, Stack } from '@mui/material';

import { trpc } from '@/api';
import { RouterButton } from '@/components/RouterButton';
import {
  announcementStartCol,
  announcementStatusCol,
  useBaseBicycleColumns,
} from '@/components/bicycles/columns';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { createUseColumns } from '@/hooks/useColumns';
import { announcementEdit } from '@/router/routes/keep/announcements';
import { DataGrid } from 'mui-ex';
import React from 'react';
import { CancelAnnouncementsDialog } from './CancelAnnouncementsDialog';
import { UpdateAnnouncementsDialog } from './UpdateAnnouncementsDialog';

const useAdditionalColumns = createUseColumns([announcementStatusCol, announcementStartCol]);

export const AnnouncementEditPage = () => {
  const { labels } = useAppContext();
  const title = announcementEdit.meta.useTitle();
  const [ids, setIds] = React.useState<Set<string>>(new Set());
  const { data: bicycles = [], isPending } = trpc.bicycles.list.useQuery({ type: 'stored' });
  const baseColumns = useBaseBicycleColumns(bicycles);
  const additionalColumns = useAdditionalColumns(bicycles);
  const columns = [...baseColumns, ...additionalColumns];
  const disabled = ids.size === 0;

  const [updateDialog, setUpdateDialog] = React.useState(false);
  const [cancelDialog, setCancelDialog] = React.useState(false);

  return (
    <MainLayout title={title}>
      <Stack component={Paper} spacing={2} sx={{ p: 2, height: 1 }}>
        <Stack sx={{ flexGrow: 1, overflow: 'auto' }}>
          <DataGrid
            columns={columns}
            rows={bicycles}
            checkboxSelection
            rowSelectionModel={{ type: 'include', ids }}
            onRowSelectionModelChange={(model) => setIds(model.ids as Set<string>)}
            disableRowSelectionOnClick
            loading={isPending}
            sx={{
              // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
              '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
              '& .MuiTablePagination-select': { mr: 2 },
            }}
          />
        </Stack>
        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <RouterButton to="/announcements">キャンセル</RouterButton>
          <Button variant="outlined" disabled={disabled} onClick={() => setCancelDialog(true)}>
            {labels.da.announce}を取り消す
          </Button>
          <CancelAnnouncementsDialog
            open={cancelDialog}
            onClose={() => setCancelDialog(false)}
            onSuccess={() => setIds(new Set())}
            bicycles={bicycles.filter((b) => ids.has(b.id))}
          />
          <Button variant="contained" disabled={disabled} onClick={() => setUpdateDialog(true)}>
            {labels.da.announce}日を設定する
          </Button>
          <UpdateAnnouncementsDialog
            open={updateDialog}
            onClose={() => setUpdateDialog(false)}
            onSuccess={() => setIds(new Set())}
            bicycles={bicycles.filter((b) => ids.has(b.id))}
          />
        </Stack>
      </Stack>
    </MainLayout>
  );
};
