import { Collapse, Grid, Typography } from '@mui/material';
import type { Control, UseFormWatch } from 'react-hook-form';
import { z } from 'zod';

import type { BicycleEvent } from 'lambda-api';
import { RhfRadio, RhfSingleSelect } from 'mui-ex';

import { useAppContext } from '@/contexts/app-context';

export const paymentOptions = [
  { label: '保管料受領済み', value: 'yes' },
  { label: '無償返還', value: 'no' },
] as const;

export const ReturnToOwnerStateSchema = z.object({
  fee: z.number().int().nonnegative(),
  payment: z.enum([paymentOptions[0].value, paymentOptions[1].value]),
  reasonId: z.string(),
});

export type ReturnToOwnerState = z.infer<typeof ReturnToOwnerStateSchema>;

export const returnToOwnerToState = (
  event: BicycleEvent | null | undefined,
): ReturnToOwnerState => {
  return {
    fee: event?.storageFeePayment?.fee ?? 0,
    payment: event?.storageFeePayment !== null ? 'yes' : 'no',
    reasonId: event?.noPaymentReasonId ?? '',
  };
};

type CommonProps = { readOnly?: boolean };
type Props<T extends { returnToOwner: ReturnToOwnerState }> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<{ returnToOwner: ReturnToOwnerState }>;
  watch: UseFormWatch<{ returnToOwner: ReturnToOwnerState }>;
};

export const RhfBicycleReturnToOwnerForm = <T extends { returnToOwner: ReturnToOwnerState }>(
  props: Props<T>,
) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  const {
    tenant: { noPaymentReasons: reasons },
  } = useAppContext();

  const fee = watch('returnToOwner.fee');
  const payment = watch('returnToOwner.payment');
  const title = payment === 'yes' ? `保管料（${fee.toLocaleString()}円）` : '保管料';

  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, md: 6 }}>
        <Typography color="textSecondary">{title}</Typography>
        <RhfRadio
          control={control}
          name="returnToOwner.payment"
          options={[...paymentOptions]}
          row
          readOnly={readOnly}
        />
        <Collapse in={payment === 'no'}>
          <RhfSingleSelect
            control={control}
            name="returnToOwner.reasonId"
            label="無償返還理由"
            options={reasons.map((r) => ({ label: r.name, value: r.id }))}
            readOnly={readOnly}
          />
        </Collapse>
      </Grid>
    </Grid>
  );
};
