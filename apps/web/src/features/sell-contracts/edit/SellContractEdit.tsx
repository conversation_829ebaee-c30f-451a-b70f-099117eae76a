import { zodResolver } from '@hookform/resolvers/zod';
import { Close, Delete, Edit } from '@mui/icons-material';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Paper,
  Stack,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { enqueueSnackbar } from 'notistack';
import React from 'react';
import { useForm } from 'react-hook-form';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { sellContractRouters } from '@/router/routes/release/sell-contracts';
import { getQueryKey } from '@trpc/react-query';
import type { Bicycles, SellContract } from 'lambda-api';
import { RhfTextField } from 'mui-ex';
import {
  type BicycleSellContractState,
  BicycleSellContractStateSchema,
  RhfBicycleSellContract,
  sellContractToState,
} from '../common/RhfBicycleSellContract';

type Props = {
  contract: SellContract;
  bicycles: Bicycles;
};

export const SellContractEdit = ({ contract: { id, ...contract }, bicycles }: Props) => {
  const { labels } = useAppContext();
  const { control, watch, handleSubmit } = useForm<BicycleSellContractState>({
    mode: 'onChange',
    defaultValues: sellContractToState(contract),
    resolver: zodResolver(BicycleSellContractStateSchema),
  });

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const onSuccess = async () => {
    const queryKeys = [
      getQueryKey(trpc.contracts.sell.list),
      getQueryKey(trpc.contracts.sell.get, { id }),
    ];
    await Promise.all(queryKeys.map((queryKey) => queryClient.invalidateQueries({ queryKey })));
    navigate({ to: '/sell-contracts' });
  };
  const updateMutation = trpc.contracts.sell.update.useMutation({ onSuccess });
  const cancelMutation = trpc.contracts.sell.cancel.useMutation({ onSuccess });
  const [cancelDialog, setCancelDialog] = React.useState(false);
  const title = sellContractRouters.meta.edit.useTitle();
  const submit = ({ status, ...input }: BicycleSellContractState) => {
    if (status !== 'canceled')
      return updateMutation.mutate(
        { id, status, ...input },
        { onSuccess: () => enqueueSnackbar('売却契約を変更しました', { variant: 'success' }) },
      );
    return setCancelDialog(true);
  };

  const submitCancel = ({ memo }: BicycleSellContractState) => {
    cancelMutation.mutate(
      { id, memo },
      { onSuccess: () => enqueueSnackbar('売却契約をキャンセルしました', { variant: 'success' }) },
    );
  };

  return (
    <MainLayout scrollable title={title}>
      <>
        <Stack component="form" noValidate spacing={2} onSubmit={handleSubmit(submit)}>
          <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
            <RhfBicycleSellContract
              control={control}
              watch={watch}
              bicycles={bicycles}
              mode="edit"
            />
          </Stack>
          <Stack alignItems="end" sx={{ mt: 2 }}>
            <TwoActionButtons
              primary={{
                type: 'submit',
                icon: Edit,
                label: '保存',
              }}
              secondary={{
                icon: Close,
                label: 'キャンセル',
                onClick: () => navigate({ to: '/sell-contracts/$id', params: { id } }),
                end: true,
              }}
            />
          </Stack>
        </Stack>
        <Dialog open={cancelDialog} onClose={() => setCancelDialog(false)}>
          <DialogTitle>{`${labels.p.sellContract}の${labels.action.cancel}`}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {`${labels.da.sell}${labels.p.contractCannotCancel}`}
            </DialogContentText>
            <RhfTextField
              control={control}
              name="memo"
              label={labels.system.memo}
              multiline
              rows={4}
              fullWidth
            />
          </DialogContent>
          <Box component="form" noValidate onSubmit={handleSubmit(submitCancel)}>
            <DialogActions>
              <TwoActionButtons
                primary={{
                  type: 'submit',
                  icon: Delete,
                  label: `${labels.business.contract}を${labels.action.cancel}する`,
                }}
                secondary={{
                  icon: Close,
                  label: `${labels.business.contract}を${labels.action.cancel}しない`,
                  onClick: () => setCancelDialog(false),
                  end: true,
                }}
              />
            </DialogActions>
          </Box>
        </Dialog>
      </>
    </MainLayout>
  );
};
