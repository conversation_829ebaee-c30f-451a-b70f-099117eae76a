import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { useParams } from '@tanstack/react-router';
import { isNullish } from 'common';
import { SellContractEdit } from './SellContractEdit';

export const SellContractEditPage = () => {
  const { id } = useParams({ from: '/app/sell-contracts/$id/edit' });
  const { data: contract } = trpc.contracts.sell.get.useQuery({ id });
  // TODO: リリース可能＋選択済みを取得する
  const { data: bicycles } = trpc.bicycles.list.useQuery();

  if (isNullish(contract) || isNullish(bicycles)) return <LoadingSpinner />;

  return <SellContractEdit contract={contract} bicycles={bicycles} />;
};
