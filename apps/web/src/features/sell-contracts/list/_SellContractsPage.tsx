import { trpc } from '@/api';
import { Paper, Stack, Typography } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import type { GridColDef } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';
import { DataGrid } from 'mui-ex';

import { useAppContext } from '@/contexts/app-context';
import { useTextToPx } from '@/hooks/useTextToPx';
import { DataGridToolbar } from '@/libs/mui-x/grid/DataGridToolbar';
import { useGridExport } from '@/libs/mui-x/grid/utils';
import { contractStatusToColor, contractStatusToIcon } from '@/models/contract';
import { sellContractRouters } from '@/router/routes/release/sell-contracts';
import { Chip } from '@mui/material';
import { getQueryKey } from '@trpc/react-query';
import type { SellContracts } from 'lambda-api';

type SellContract = SellContracts[number];
type ColDef = GridColDef<SellContract> & {
  field: keyof SellContract | `dealer.${keyof NonNullable<SellContract['dealer']>}`;
};

export const useColumns = () => {
  const { labels } = useAppContext();
  const fit = useTextToPx();

  const statusCol: ColDef = {
    field: 'status',
    headerName: '契約ステータス',
    width: fit('契約ステータス'),
    valueGetter: (_, row) => labels.contractStatus[row.status],
    renderCell: ({ row }) => {
      const label = labels.contractStatus[row.status];
      const Icon = contractStatusToIcon(row.status);
      const color = contractStatusToColor(row.status);
      return (
        <Chip
          label={label}
          icon={<Icon style={{ color: color['300'] }} />}
          sx={{ bgcolor: color['50'] }}
        />
      );
    },
  };
  const dealerNameCol: ColDef = {
    field: 'dealer.name',
    headerName: '購入者',
    width: fit('●●●●＠●●●●＠'),
    valueGetter: (_, row) => row.dealer?.name,
  };
  const countCol: ColDef = {
    headerName: '台数',
    field: '_count',
    type: 'number',
    valueGetter: (_, row) => `${row._count.events.toLocaleString()} 台`,
  };
  const priceCol: ColDef = {
    field: 'price',
    headerName: labels.business.price,
    width: fit('100,000 円'),
    type: 'number',
    valueFormatter: (value: number | null) => (value ? `${value.toLocaleString()} 円` : '-'),
  };

  return [statusCol, dealerNameCol, countCol, priceCol];
};

const useSellContractExport = () => {
  const columns = useColumns();
  return useGridExport(columns, sellContractRouters.meta.list.useTitle());
};

const Toolbar = () => {
  const exportFormatter = useSellContractExport();
  return (
    <DataGridToolbar
      to="/sell-contracts/create"
      queryKey={getQueryKey(trpc.contracts.sell.list)}
      exportFormatter={exportFormatter}
    />
  );
};

export const SellContractsPage = () => {
  const columns = useColumns();
  const { data: contracts = [], isPending } = trpc.contracts.sell.list.useQuery();
  const navigate = useNavigate();
  const handleRowClick = (params: GridRowParams) => {
    navigate({ to: '/sell-contracts/$id', params: { id: params.id.toString() } });
  };
  return (
    <Stack spacing={1} sx={{ p: 2, height: 1 }}>
      <Typography variant="h4">{sellContractRouters.meta.list.useTitle()}</Typography>
      <Paper sx={{ flexGrow: 1, overflow: 'auto' }}>
        <DataGrid
          persistent="/sell-contracts"
          columns={columns}
          rows={contracts}
          onRowClick={handleRowClick}
          disableRowSelectionOnClick
          slots={{ toolbar: Toolbar }}
          loading={isPending}
          sx={{
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 2 },
          }}
        />
      </Paper>
    </Stack>
  );
};
