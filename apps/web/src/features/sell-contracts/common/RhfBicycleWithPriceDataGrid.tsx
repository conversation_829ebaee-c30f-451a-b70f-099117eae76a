import {
  Box,
  FormHelperText,
  Icon<PERSON>utton,
  Stack,
  type SxProps,
  type Theme,
  alpha,
} from '@mui/material';
import { type Control, useController } from 'react-hook-form';
import { z } from 'zod';

import { DataGrid, type DataGridProps } from 'mui-ex';

import type { GridRowParams } from '@mui/x-data-grid';
import type { Bicycles } from 'lambda-api';

import { conditionCol, imagesCol, statusCol } from '@/components/bicycles/columns';
import { type ColDefFactory, createUncheckedUseColumns } from '@/hooks/useColumns';
import { Edit } from '@mui/icons-material';

export const BicycleState = z.object({
  id: z.string(),
  price: z.number().nullable(),
});
type BicycleStateType = z.infer<typeof BicycleState>;

export const BicycleWithPriceDataGridStateSchema = z.object({
  bicycles: z.array(BicycleState),
});
export type BicycleWithPriceDataGridState = z.infer<typeof BicycleWithPriceDataGridStateSchema>;

type CommonProps = {
  rows: Bicycles;
  loading?: boolean;
  readOnly?: boolean;
  slots?: DataGridProps['slots'];
  hideFooterSelectedRowCount?: boolean;
  isRowSelectable?: (params: GridRowParams<Bicycles[number]>) => boolean;
  sx?: SxProps<Theme>;
};
type Props<T extends BicycleWithPriceDataGridState> = CommonProps & {
  control: Control<T>;
};
type InnerProps = CommonProps & {
  control: Control<BicycleWithPriceDataGridState>;
};

type Merged = Bicycles[number] & BicycleStateType;

const valueCol: ColDefFactory<Merged> = () => {
  return {
    field: 'price',
    headerName: '価格',
    width: 120,
    editable: true,
    renderCell: (params) => {
      const value = params.value;
      const label = value !== null ? `${value} 円` : '未設定';
      const handleClick = () =>
        params.api.startCellEditMode({ id: params.id, field: params.field });

      return (
        <Stack direction="row" justifyContent="right" alignItems="center" spacing={1}>
          {label}
          <Box>
            <IconButton
              color="primary"
              size="small"
              onClick={handleClick}
              disabled={!params.isEditable}
            >
              <Edit fontSize="inherit" />
            </IconButton>
          </Box>
        </Stack>
      );
    },
    preProcessEditCellProps: (params) => {
      // TODO: エラー理由をユーザーに示したい
      const error = !/^[1-9]?[0-9]*$/.test(String(params.props.value));
      return { ...params.props, error };
    },
  };
};

const useBicycleSellColumns = createUncheckedUseColumns([
  imagesCol,
  statusCol,
  conditionCol,
  valueCol,
]);

export const RhfBicycleWithPriceDataGrid = <T extends BicycleWithPriceDataGridState>(
  props: Props<T>,
) => {
  const {
    control,
    rows,
    loading,
    readOnly,
    slots,
    hideFooterSelectedRowCount,
    isRowSelectable,
    sx,
  } = props as unknown as InnerProps;

  const {
    field: { value: values, onChange },
    fieldState: { error },
  } = useController({ control, name: 'bicycles' });

  const ids = new Set(values.map(({ id }) => id));
  const editableProps: Pick<
    DataGridProps,
    | 'checkboxSelection'
    | 'rowSelectionModel'
    | 'isCellEditable'
    | 'onRowSelectionModelChange'
    | 'processRowUpdate'
  > = !readOnly
    ? {
        checkboxSelection: true,
        rowSelectionModel: { type: 'include', ids },
        isCellEditable: (params) => {
          if (readOnly) return false;
          return ids.has(params.row.id);
        },
        onRowSelectionModelChange: ({ ids }) => {
          const bicycles = [...ids].map((id) => {
            const row = rows.find((r) => r.id === id);
            const price = row?.assessment?.last?.price ?? 0;
            return { id, price };
          });
          onChange(bicycles);
        },
        processRowUpdate: (next: Merged, current: Merged) => {
          if (next.price === current.price) return current;
          onChange(
            values.map((v) => (v.id === next.id ? { id: next.id, price: Number(next.price) } : v)),
          );
          return next;
        },
      }
    : {};

  const rowsWithPrice: Merged[] = rows.map((row) => ({
    ...row,
    price: row.assessment?.last?.price ?? null,
  }));
  const columns = useBicycleSellColumns(rows);

  return (
    <>
      <DataGrid
        columns={columns}
        rows={rowsWithPrice}
        {...editableProps}
        disableRowSelectionOnClick
        hideFooterSelectedRowCount={hideFooterSelectedRowCount}
        isRowSelectable={isRowSelectable}
        slots={slots}
        loading={loading}
        sx={[
          {
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 2 },
            '& .Mui-error': { bgcolor: (t) => alpha(t.palette.error.light, 0.2) },
          },
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      />
      {error && <FormHelperText error>{error.message}</FormHelperText>}
    </>
  );
};
