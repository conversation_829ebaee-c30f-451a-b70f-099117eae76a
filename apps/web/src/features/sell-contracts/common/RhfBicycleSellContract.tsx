import {
  Box,
  FormControl,
  FormControlLabel,
  Grid,
  InputAdornment,
  Switch,
  TextField,
} from '@mui/material';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { BicycleContractStatusSchema } from 'common';
import type { Bicycles, SellContract } from 'lambda-api';
import { RhfDatePicker, RhfSingleSelect, RhfTextField } from 'mui-ex';
import React from 'react';
import type { Control, UseFormWatch } from 'react-hook-form';
import { z } from 'zod';

import { BicycleState, RhfBicycleWithPriceDataGrid } from './RhfBicycleWithPriceDataGrid';

export const BicycleSellContractStateSchema = z
  .object({
    dealerId: z
      .string()
      .nullable()
      .transform((v) => (v !== '' ? v : null)),
    date: z.coerce.date().nullable(),
    status: BicycleContractStatusSchema,
    bicycles: z.array(BicycleState).min(1),
    memo: z.string(),
  })
  .superRefine((arg, ctx) => {
    if (arg.status === 'done') {
      if (arg.dealerId === null || arg.dealerId === '') {
        ctx.addIssue({
          message: '購入者が未選択です',
          code: z.ZodIssueCode.custom,
          path: ['dealerId'],
        });
      }
      if (arg.date === null) {
        ctx.addIssue({
          message: '売却日が未入力です',
          code: z.ZodIssueCode.custom,
          path: ['date'],
        });
      }
    }
  });
export type BicycleSellContractState = z.infer<typeof BicycleSellContractStateSchema>;

export const sellContractToState = (
  contract: Omit<SellContract, 'id'>,
): BicycleSellContractState => {
  return {
    dealerId: contract.dealerId ?? '',
    date: contract.date ? new Date(contract.date) : null,
    status: contract.status,
    bicycles: contract.bicycles.map((b) => ({
      id: b.id,
      price: b.assessment?.last?.price ?? 0,
    })),
    memo: contract.memo ?? '',
  };
};
type Mode = 'create' | 'edit';
type CommonProps = { readOnly?: boolean; bicycles: Bicycles; mode?: Mode };
type Props<T extends BicycleSellContractState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<BicycleSellContractState>;
  watch: UseFormWatch<BicycleSellContractState>;
};

const collapsedSize = 400;

export const RhfBicycleSellContract = <T extends BicycleSellContractState>(props: Props<T>) => {
  const { control, watch, readOnly, bicycles, mode } = props as unknown as InnerProps;
  const { labels } = useAppContext();
  const { data: dealers = [] } = trpc.dealers.list.useQuery({ dealTypes: ['sell'] });
  const [filter, setFilter] = React.useState(false);

  const status = watch('status');
  const bicycleStateItems = watch('bicycles');
  const selectedBicycles = bicycles.filter((b) =>
    bicycleStateItems.some((item) => b.id === item.id),
  );
  const rows = filter ? selectedBicycles : bicycles;
  const selectable = !filter && !readOnly;

  const amount = bicycleStateItems.reduce((sum, b) => sum + (b.price ?? 0), 0);
  const options = BicycleContractStatusSchema.options
    .filter((o) => mode !== 'create' || o !== 'canceled')
    .map((value) => ({
      label: labels.contractStatus[value],
      value,
    }));
  return (
    <>
      <RhfSingleSelect
        control={control}
        name="dealerId"
        label="購入者"
        options={dealers.map((dealer) => ({ label: dealer.name, value: dealer.id }))}
        readOnly={readOnly}
      />
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <RhfSingleSelect
            control={control}
            name="status"
            label={labels.p.contractStatus}
            options={options}
            readOnly={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <RhfDatePicker
            control={control}
            name="date"
            label={status === 'scheduled' ? '売却予定日' : '売却日'}
            clearable={status === 'scheduled'}
            readOnly={readOnly}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            label="台数"
            value={bicycleStateItems.length}
            slotProps={{
              input: {
                endAdornment: <InputAdornment position="end">台</InputAdornment>,
              },
              htmlInput: { readOnly: true, style: { textAlign: 'right' } },
            }}
            focused={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            label="合計売却金額"
            value={amount.toLocaleString()}
            slotProps={{
              input: {
                endAdornment: <InputAdornment position="end">円</InputAdornment>,
              },
              htmlInput: { readOnly: true, style: { textAlign: 'right' } },
            }}
            focused={readOnly}
          />
        </Grid>
      </Grid>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxHeight: selectable ? collapsedSize : undefined,
        }}
      >
        <RhfBicycleWithPriceDataGrid
          control={control}
          rows={rows}
          readOnly={!selectable}
          hideFooterSelectedRowCount
        />
      </Box>
      {!readOnly && (
        <FormControl fullWidth>
          <FormControlLabel
            label="選択した車両のみを表示する"
            control={
              <Switch
                onChange={(_, checked) => setFilter(checked)}
                checked={filter}
                disabled={bicycleStateItems.length === 0}
              />
            }
          />
        </FormControl>
      )}
      <RhfTextField
        control={control}
        name="memo"
        label={labels.system.memo}
        multiline
        rows={4}
        readOnly={readOnly}
      />
    </>
  );
};
