import { Box, FormHelperText, Paper, Stack } from '@mui/material';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { RouterButton } from '@/components/RouterButton';
import { sellContractRouters } from '@/router/routes/release/sell-contracts';
import { Edit } from '@mui/icons-material';
import type { SellContract } from 'lambda-api';
import {
  type BicycleSellContractState,
  BicycleSellContractStateSchema,
  RhfBicycleSellContract,
  sellContractToState,
} from '../common/RhfBicycleSellContract';

type Props = {
  contract: SellContract;
};

export const SellContractDetails = ({ contract }: Props) => {
  const { control, watch } = useForm<BicycleSellContractState>({
    mode: 'onChange',
    defaultValues: sellContractToState(contract),
    resolver: zodResolver(BicycleSellContractStateSchema),
  });

  const title = sellContractRouters.meta.details.useTitle();

  const { bicycles } = contract;

  return (
    <MainLayout scrollable title={title}>
      <>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfBicycleSellContract control={control} watch={watch} bicycles={bicycles} readOnly />
        </Stack>
        <Stack alignItems="end" sx={{ mt: 2 }}>
          <Box>
            <RouterButton
              to="/sell-contracts/$id/edit"
              params={{ id: contract.id }}
              startIcon={<Edit />}
              disabled={contract.status === 'canceled'}
            >
              編集
            </RouterButton>
            {contract.status === 'canceled' && (
              <FormHelperText>キャンセルされた契約は編集できません</FormHelperText>
            )}
          </Box>
        </Stack>
      </>
    </MainLayout>
  );
};
