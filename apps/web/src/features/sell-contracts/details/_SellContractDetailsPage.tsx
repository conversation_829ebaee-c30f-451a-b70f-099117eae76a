import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { useParams } from '@tanstack/react-router';
import { isNullish } from 'common';
import { SellContractDetails } from './SellContractDetails';

export const SellContractDetailsPage = () => {
  const { id } = useParams({ from: '/app/sell-contracts/$id' });
  const { data: contract } = trpc.contracts.sell.get.useQuery({ id });

  if (isNullish(contract)) return <LoadingSpinner />;

  return <SellContractDetails contract={contract} />;
};
