import { Alert, Paper, Stack } from '@mui/material';
import { enqueueSnackbar } from 'notistack';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { zodResolver } from '@hookform/resolvers/zod';
import { Add, Close } from '@mui/icons-material';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';

import { useAppContext } from '@/contexts/app-context';
import { sellContractRouters } from '@/router/routes/release/sell-contracts';
import {
  type BicycleSellContractState,
  BicycleSellContractStateSchema,
  RhfBicycleSellContract,
} from '../common/RhfBicycleSellContract';

export const SellContractCreatePage = () => {
  const { labels } = useAppContext();
  const { control, handleSubmit, watch, formState } = useForm<BicycleSellContractState>({
    mode: 'onChange',
    defaultValues: {
      bicycles: [],
      dealerId: '',
      status: 'scheduled',
      date: new Date(),
      memo: '',
    } as const satisfies BicycleSellContractState,
    resolver: zodResolver(BicycleSellContractStateSchema),
  });

  const { data: bicycles = [] } = trpc.bicycles.list.useQuery({ type: 'release' });
  const { error, mutate } = trpc.contracts.sell.create.useMutation();

  const navigate = useNavigate();
  const title = sellContractRouters.meta.create.useTitle();
  const submit = (input: BicycleSellContractState) => {
    console.log(input);
    mutate(input, {
      onSuccess: () => {
        navigate({ to: '/sell-contracts' });
        enqueueSnackbar(
          `新規の売却契約${input.status === 'scheduled' ? '(予定)' : ''}を作成しました`,
          {
            variant: 'success',
          },
        );
      },
    });
  };
  return (
    <MainLayout title={title}>
      <form noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfBicycleSellContract
            control={control}
            watch={watch}
            bicycles={bicycles}
            mode="create"
          />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
          <Stack alignItems="end">
            <TwoActionButtons
              primary={{
                type: 'submit',
                icon: Add,
                label: labels.doing.create,
                loading: formState.isSubmitting,
              }}
              secondary={{
                icon: Close,
                end: true,
                label: labels.doing.cancel,
                onClick: () => navigate({ to: '/sell-contracts' }),
              }}
            />
          </Stack>
        </Stack>
      </form>
    </MainLayout>
  );
};
