import { <PERSON>, Button, Paper, Stack, Typography } from '@mui/material';
import { z } from 'zod';

import { trpc } from '@/api';

import {
  imagesCol,
  numberedAtCol,
  removedAtCol,
  serialNoCol,
  statusCol,
} from '@/components/bicycles/columns';
import { RhfBicycleDataGrid } from '@/components/bicycles/react-hook-form/RhfBicycleDataGrid';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { createUseColumns } from '@/hooks/useColumns';
import { useInvalidateBicycles } from '@/models/bicycle';
import { serialTagNumberingMeta } from '@/router/routes/keep/serial-tag-numbering-route';
import { usePositionEffect } from '@/stores/position';
import { zodResolver } from '@hookform/resolvers/zod';
import { closest } from 'common';
import { RhfSingleSelect } from 'mui-ex';
import { enqueueSnackbar } from 'notistack';
import { useController, useForm } from 'react-hook-form';

const DataGridToolbar = () => {
  const { labels } = useAppContext();
  return (
    <Box sx={{ p: 1 }}>
      <Typography color="textSecondary">{labels.domain.bicycle}を選択してください</Typography>
    </Box>
  );
};

const StateSchema = z.object({
  storageId: z.string().min(1),
  bicycleIds: z.array(z.string()),
});
type State = z.infer<typeof StateSchema>;

const useColumns = createUseColumns([
  imagesCol,
  statusCol,
  removedAtCol,
  numberedAtCol,
  serialNoCol,
]);

export const SerialTagNumberingPage = () => {
  const { labels, storages } = useAppContext();
  const { data: bicycles = [] } = trpc.bicycles.list.useQuery({ type: 'number' });
  const { control, handleSubmit, watch } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      storageId: '',
      bicycleIds: [],
    } satisfies State,
    resolver: zodResolver(StateSchema),
  });
  const storageIdCtrl = useController({ control, name: 'storageId' });
  usePositionEffect(async (position) => {
    const storage = closest(position, storages);
    storageIdCtrl.field.onChange(storage?.id ?? '');
  });

  const invalidate = useInvalidateBicycles();
  const bicycleIdsCtrl = useController({ control, name: 'bicycleIds' });
  const { mutate } = trpc.serialTags.create.useMutation({
    onSuccess: () => {
      invalidate('number');
      invalidate('print');
      bicycleIdsCtrl.field.onChange([]);
      enqueueSnackbar('整理番号を採番しました', { variant: 'success' });
    },
  });

  const submit = async (input: State) => mutate(input);

  const storageId = watch('storageId');
  const rows = bicycles.filter((b) => {
    if (b.serialTag === null) return true;
    return b.serialTag.last?.storageId === storageId;
  });
  const columns = useColumns(rows);
  const title = serialTagNumberingMeta.useTitle();
  return (
    <MainLayout title={title}>
      <Paper sx={{ p: 2, height: 1 }}>
        <Stack
          component="form"
          noValidate
          spacing={2}
          onSubmit={handleSubmit(submit)}
          sx={{ height: 1 }}
        >
          <RhfSingleSelect
            control={control}
            name="storageId"
            label={labels.domain.storage}
            options={storages.map((s) => ({ label: s.name, value: s.id }))}
          />
          <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
            <RhfBicycleDataGrid
              control={control}
              columns={columns}
              rows={rows}
              slots={{ toolbar: DataGridToolbar }}
              hideFooterSelectedRowCount
              isRowSelectable={({ row }) => row.serialTag === null}
            />
          </Box>
          <Stack alignItems="end">
            <Button type="submit" variant="contained">
              整理番号を採番する
            </Button>
          </Stack>
        </Stack>
      </Paper>
    </MainLayout>
  );
};
