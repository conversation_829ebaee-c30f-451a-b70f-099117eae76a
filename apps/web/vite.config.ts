/// <reference types="vitest" />
import { defineConfig } from 'vite';

import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: [['@locator/babel-jsx/dist', { env: 'development' }]],
      },
    }),
  ],
  resolve: {
    alias: { '@/': `${__dirname}/src/` },
  },
  // test: {
  //   includeSource: ['src/**/*.ts'],
  // },
  // server: {
  //   hmr: false,
  // },
});
