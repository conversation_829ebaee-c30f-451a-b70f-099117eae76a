I got Error when create Inquiry. let analize and fix for me.

- ✅️backend of create inquiry:
	- lambdas/api/src/procedures/inquiries/create.ts
- ✅️frontend code:
	- apps/web/src/features/inquiries/common/RhfInquiry.tsx
	- apps/web/src/features/inquiries/create/_InquiryCreatePage.tsx


- ✅️payload send to backend:

```
{"0":{"json":{"id":"0e2003f7-bf62-4a08-827a-00da51742779","receptionRouteId":"9e9c4a41-45ba-4436-be06-2c9e8aa3410d","receiverId":"b7d16563-98af-4189-9c89-8712eccb379c","receivedAt":"2025-07-06T20:25:00.000Z","title":"タイトル","description":"問い合わせ内容\n","address":"東京都渋谷区恵比寿恵比寿ガーデンプレイス","memo":"biko","images":[]},"meta":{"values":{"receivedAt":["Date"]}}}}
```



- ✅️error messsage:


```
[{
    "error": {
        "json": {
            "message": "\nInvalid `prisma.inquiry.create()` invocation:\n\n\nUnique constraint failed on the (not available)",
            "code": -32603,
            "data": {
                "code": "INTERNAL_SERVER_ERROR",
                "httpStatus": 500,
                "stack": "PrismaClientKnownRequestError: \nInvalid `prisma.inquiry.create()` invocation:\n\n\nUnique constraint failed on the (not available)\n    at t4.handleRequestError (file:///var/task/index.mjs:122:7458)\n    at t4.handleAndLogRequestError (file:///var/task/index.mjs:122:6782)\n    at t4.request (file:///var/task/index.mjs:122:6489)\n    at async u (file:///var/task/index.mjs:131:9790)\n    at async file:///var/task/index.mjs:3141:25649\n    at async Proxy._transactionWithCallback (file:///var/task/index.mjs:131:8151)\n    at async e8 (file:///var/task/index.mjs:3141:25588)\n    at async Qn.middlewares (file:///var/task/index.mjs:3102:206325)\n    at async Eb (file:///var/task/index.mjs:3105:69)\n    at async Eb (file:///var/task/index.mjs:3105:69)",
                "path": "inquiries.create"
            }
        }
    }
}]


```



✅️ Let understand about my project and coding rules.
```

This project called "maphin" handles the task creation flow in a multi-tenant SaaS platform.
It includes a form using **React Hook Form + Zod** for validation, and uses **React Query** for submitting to a backend API.

**📦 Stack**:
React + TypeScript + React Hook Form + Zod + React Query + Material UI + Prisma(Nodejs + Postgre sql), Material UI

**📦 Local execute: **
- Macbook 2020 intell chip
- Volta for npm version manager
- profile : maphin
- environment: xcong ( other env is fuka, stage, prod, just use xcong is enough)


Rule:
- If database prisma schema include version(eg: versions   Inquiry[] @relation("versions") ) , let consider create version in nodejs backend to check log.

```
